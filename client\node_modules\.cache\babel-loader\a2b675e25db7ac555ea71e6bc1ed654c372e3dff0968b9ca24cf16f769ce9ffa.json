{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: typeof question.correctAnswer === 'string' ? question.correctAnswer : String(question.correctAnswer || ''),\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail';\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          points: correctAnswers * 10\n        }\n      };\n      try {\n        const response = await addReport(reportData);\n        if (response.success) {\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: {\n                percentage,\n                correctAnswers,\n                totalQuestions: questions.length,\n                timeTaken,\n                resultDetails\n              }\n            });\n          });\n        } else {\n          message.error(response.message || 'Failed to submit quiz');\n        }\n      } catch (apiError) {\n        console.error('API Error during submission:', apiError);\n        message.error('Network error while submitting quiz');\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n    // Debug logging\n    console.log('Current question:', {\n      type: currentQ.type,\n      answerType: currentQ.answerType,\n      options: currentQ.options,\n      optionsType: typeof currentQ.options,\n      optionsLength: Array.isArray(currentQ.options) ? currentQ.options.length : 'Not array'\n    });\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    if (!currentQ.options || !Array.isArray(currentQ.options) || currentQ.options.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"No options available for this question.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: currentQ.options.map((option, index) => {\n        const optionLetter = String.fromCharCode(65 + index);\n        const isSelected = answers[currentQuestion] === option;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleAnswerSelect(option),\n          className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n              children: optionLetter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg leading-relaxed flex-1 text-left\",\n              children: typeof option === 'string' ? option : JSON.stringify(option)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-800 text-sm font-medium mb-2\",\n          children: \"Fill in the blank:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700\",\n          children: \"Type your answer in the box below\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: answers[currentQuestion] || '',\n          onChange: e => handleAnswerSelect(e.target.value),\n          placeholder: \"Type your answer here...\",\n          className: \"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this);\n  }\n  if (!quiz || !questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Question Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Unable to load the current question.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Invalid Question Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"The question data is corrupted or invalid.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => startTransition(() => navigate('/quiz')),\n              className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-6 h-6 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: quiz.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Question \", currentQuestion + 1, \" of \", questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 px-4 py-2 rounded-lg ${timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${(currentQuestion + 1) / questions.length * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-4\",\n            children: typeof currentQ.name === 'string' ? currentQ.name : 'Question'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), currentQ.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 bg-gray-50 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQ.image,\n              alt: \"Question diagram\",\n              className: \"max-w-full h-auto rounded-lg shadow-lg mx-auto block\",\n              style: {\n                maxHeight: '400px'\n              },\n              onError: e => {\n                e.target.style.display = 'none';\n                // Show fallback message\n                const fallback = document.createElement('div');\n                fallback.className = 'text-center py-8 text-gray-500';\n                fallback.innerHTML = '<p>Could not load diagram</p>';\n                e.target.parentNode.appendChild(fallback);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 mb-8\",\n          children: renderAnswerSection()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestion === 0,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${currentQuestion === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSubmitQuiz,\n            className: \"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), \"Submit Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 414,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"fY1y2RfP35t8yXNHY5IyCXmOpUg=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "message", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "id", "navigate", "user", "state", "loading", "setLoading", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "console", "log", "_id", "token", "localStorage", "getItem", "error", "response", "examId", "success", "data", "length", "Array", "fill", "duration", "Date", "handleSubmitQuiz", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "questionId", "questionName", "name", "String", "percentage", "round", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "points", "totalQuestions", "apiError", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "renderAnswerSection", "questionType", "currentQ", "type", "answerType", "options", "optionsType", "optionsLength", "isArray", "toLowerCase", "renderMultipleChoice", "renderFillInTheBlank", "renderImageQuestion", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "option", "optionLetter", "fromCharCode", "isSelected", "onClick", "stringify", "value", "onChange", "e", "target", "placeholder", "autoFocus", "isLastQuestion", "style", "width", "image", "src", "alt", "maxHeight", "onError", "display", "fallback", "document", "createElement", "innerHTML", "parentNode", "append<PERSON><PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { \n  Tb<PERSON><PERSON>, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        \n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: typeof question.correctAnswer === 'string' ? question.correctAnswer : String(question.correctAnswer || ''),\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          points: correctAnswers * 10\n        }\n      };\n\n      try {\n        const response = await addReport(reportData);\n\n        if (response.success) {\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: {\n                percentage,\n                correctAnswers,\n                totalQuestions: questions.length,\n                timeTaken,\n                resultDetails\n              }\n            });\n          });\n        } else {\n          message.error(response.message || 'Failed to submit quiz');\n        }\n      } catch (apiError) {\n        console.error('API Error during submission:', apiError);\n        message.error('Network error while submitting quiz');\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n    // Debug logging\n    console.log('Current question:', {\n      type: currentQ.type,\n      answerType: currentQ.answerType,\n      options: currentQ.options,\n      optionsType: typeof currentQ.options,\n      optionsLength: Array.isArray(currentQ.options) ? currentQ.options.length : 'Not array'\n    });\n\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    if (!currentQ.options || !Array.isArray(currentQ.options) || currentQ.options.length === 0) {\n      return (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-600\">No options available for this question.</p>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {currentQ.options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index);\n          const isSelected = answers[currentQuestion] === option;\n\n          return (\n            <button\n              key={index}\n              onClick={() => handleAnswerSelect(option)}\n              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {optionLetter}\n                </div>\n                <span className=\"text-lg leading-relaxed flex-1 text-left\">\n                  {typeof option === 'string' ? option : JSON.stringify(option)}\n                </span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <p className=\"text-blue-800 text-sm font-medium mb-2\">Fill in the blank:</p>\n          <p className=\"text-gray-700\">Type your answer in the box below</p>\n        </div>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={answers[currentQuestion] || ''}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\"\n            autoFocus\n          />\n        </div>\n      </div>\n    );\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Question Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">Unable to load the current question.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Invalid Question Data</h2>\n            <p className=\"text-gray-600 mb-6\">The question data is corrupted or invalid.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <button\n                onClick={() => startTransition(() => navigate('/quiz'))}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <TbArrowLeft className=\"w-6 h-6 text-gray-600\" />\n              </button>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{quiz.name}</h1>\n                <p className=\"text-sm text-gray-600\">\n                  Question {currentQuestion + 1} of {questions.length}\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${\n                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}>\n                <TbClock className=\"w-5 h-5\" />\n                <span className=\"font-semibold\">{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n          </div>\n          \n          {/* Progress bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto p-6\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\">\n          {/* Question */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}\n            </h2>\n            \n            {currentQ.image && (\n              <div className=\"mb-6 bg-gray-50 rounded-lg p-4\">\n                <img\n                  src={currentQ.image}\n                  alt=\"Question diagram\"\n                  className=\"max-w-full h-auto rounded-lg shadow-lg mx-auto block\"\n                  style={{ maxHeight: '400px' }}\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    // Show fallback message\n                    const fallback = document.createElement('div');\n                    fallback.className = 'text-center py-8 text-gray-500';\n                    fallback.innerHTML = '<p>Could not load diagram</p>';\n                    e.target.parentNode.appendChild(fallback);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Section - Different types based on question type */}\n          <div className=\"space-y-4 mb-8\">\n            {renderAnswerSection()}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex justify-between items-center\">\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                className=\"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\"\n              >\n                <TbCheck className=\"w-5 h-5\" />\n                Submit Quiz\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbArrowRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAK,CAAC,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFb,UAAU,CAAC,IAAI,CAAC;QAChBc,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpB,EAAE,CAAC;QAExC,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACmB,GAAG,EAAE;UACtB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAI,CAACF,KAAK,EAAE;YACVH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnD/B,OAAO,CAACoC,KAAK,CAAC,gCAAgC,CAAC;YAC/CxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;QAEA,MAAMyB,QAAQ,GAAG,MAAMhC,WAAW,CAAC;UAAEiC,MAAM,EAAE3B;QAAG,CAAC,CAAC;QAClDmB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClBxC,OAAO,CAACoC,KAAK,CAAC,qBAAqB,CAAC;YACpCxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAACyB,QAAQ,CAACG,IAAI,CAACrB,SAAS,IAAIkB,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAACsB,MAAM,KAAK,CAAC,EAAE;YACpEzC,OAAO,CAACoC,KAAK,CAAC,sCAAsC,CAAC;YACrDxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAM,OAAO,CAACmB,QAAQ,CAACG,IAAI,CAAC;UACtBpB,YAAY,CAACiB,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAAC;UACrCK,UAAU,CAAC,IAAIkB,KAAK,CAACL,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAACsB,MAAM,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;UAC9DjB,WAAW,CAACW,QAAQ,CAACG,IAAI,CAACI,QAAQ,GAAG,EAAE,CAAC;UACxChB,YAAY,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;UACxBf,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEM,QAAQ,CAACG,IAAI,CAAC;QACzD,CAAC,MAAM;UACLV,OAAO,CAACM,KAAK,CAAC,iBAAiB,EAAEC,QAAQ,CAACrC,OAAO,CAAC;UAClDA,OAAO,CAACoC,KAAK,CAACC,QAAQ,CAACrC,OAAO,IAAI,qBAAqB,CAAC;UACxDJ,eAAe,CAAC,MAAM;YACpBgB,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CpC,OAAO,CAACoC,KAAK,CAAC,wCAAwC,CAAC;QACvDxC,eAAe,CAAC,MAAM;UACpBgB,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIL,EAAE,IAAIE,IAAI,EAAE;MACdgB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAClB,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMiC,gBAAgB,GAAGnD,WAAW,CAAC,YAAY;IAC/C,IAAI;MAEF,IAAIoD,WAAW,GAAGlC,IAAI;MACtB,IAAI,CAACkC,WAAW,IAAI,CAACA,WAAW,CAACf,GAAG,EAAE;QACpC,MAAMgB,UAAU,GAAGd,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIa,UAAU,EAAE;UACd,IAAI;YACFD,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOZ,KAAK,EAAE;YACdN,OAAO,CAACM,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvDxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACmC,WAAW,IAAI,CAACA,WAAW,CAACf,GAAG,EAAE;QACpChC,OAAO,CAACoC,KAAK,CAAC,2CAA2C,CAAC;QAC1DxC,eAAe,CAAC,MAAM;UACpBgB,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAMuC,OAAO,GAAG,IAAIN,IAAI,CAAC,CAAC;MAC1B,MAAMO,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGxB,SAAS,IAAI,IAAI,CAAC;MAE1D,IAAI4B,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGrC,SAAS,CAACsC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGrC,OAAO,CAACoC,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLQ,UAAU,EAAEL,QAAQ,CAAC1B,GAAG,IAAK,YAAW2B,KAAM,EAAC;UAC/CK,YAAY,EAAE,OAAON,QAAQ,CAACO,IAAI,KAAK,QAAQ,GAAGP,QAAQ,CAACO,IAAI,GAAI,YAAWN,KAAK,GAAG,CAAE,EAAC;UACzFC,UAAU,EAAE,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGM,MAAM,CAACN,UAAU,IAAI,EAAE,CAAC;UAClFE,aAAa,EAAE,OAAOJ,QAAQ,CAACI,aAAa,KAAK,QAAQ,GAAGJ,QAAQ,CAACI,aAAa,GAAGI,MAAM,CAACR,QAAQ,CAACI,aAAa,IAAI,EAAE,CAAC;UACzHD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMM,UAAU,GAAGd,IAAI,CAACe,KAAK,CAAEb,cAAc,GAAGpC,SAAS,CAACsB,MAAM,GAAI,GAAG,CAAC;MACxE,MAAM4B,OAAO,GAAGF,UAAU,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM;MAElD,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAE5D,EAAE;QACRE,IAAI,EAAEkC,WAAW,CAACf,GAAG;QACrBwC,MAAM,EAAE;UACNjB,cAAc;UACdkB,YAAY,EAAEtD,SAAS,CAACsB,MAAM,GAAGc,cAAc;UAC/CY,UAAU;UACVO,KAAK,EAAEP,UAAU;UACjBE,OAAO,EAAEA,OAAO;UAChBjB,SAAS;UACTuB,MAAM,EAAEpB,cAAc,GAAG;QAC3B;MACF,CAAC;MAED,IAAI;QACF,MAAMlB,QAAQ,GAAG,MAAM/B,SAAS,CAACgE,UAAU,CAAC;QAE5C,IAAIjC,QAAQ,CAACE,OAAO,EAAE;UACpB3C,eAAe,CAAC,MAAM;YACpBgB,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;cAC7BG,KAAK,EAAE;gBACLqD,UAAU;gBACVZ,cAAc;gBACdqB,cAAc,EAAEzD,SAAS,CAACsB,MAAM;gBAChCW,SAAS;gBACTI;cACF;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLxD,OAAO,CAACoC,KAAK,CAACC,QAAQ,CAACrC,OAAO,IAAI,uBAAuB,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO6E,QAAQ,EAAE;QACjB/C,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEyC,QAAQ,CAAC;QACvD7E,OAAO,CAACoC,KAAK,CAAC,qCAAqC,CAAC;MACtD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CpC,OAAO,CAACoC,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC,EAAE,CAACT,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEZ,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEvD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAI+B,QAAQ,IAAI,CAAC,EAAE;MACjB;MACA;IACF;IAEA,MAAMqD,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BrD,WAAW,CAACsD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACrD,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMyD,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC,GAAG7D,OAAO,CAAC;IAC/B6D,UAAU,CAAC/D,eAAe,CAAC,GAAG8D,MAAM;IACpC3D,UAAU,CAAC4D,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIhE,eAAe,GAAGF,SAAS,CAACsB,MAAM,GAAG,CAAC,EAAE;MAC1CnB,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMiE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjE,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMkE,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGpC,IAAI,CAACC,KAAK,CAACkC,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,YAAY,GAAGC,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACE,UAAU,IAAI,KAAK;;IAElE;IACAnE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BiE,IAAI,EAAED,QAAQ,CAACC,IAAI;MACnBC,UAAU,EAAEF,QAAQ,CAACE,UAAU;MAC/BC,OAAO,EAAEH,QAAQ,CAACG,OAAO;MACzBC,WAAW,EAAE,OAAOJ,QAAQ,CAACG,OAAO;MACpCE,aAAa,EAAE1D,KAAK,CAAC2D,OAAO,CAACN,QAAQ,CAACG,OAAO,CAAC,GAAGH,QAAQ,CAACG,OAAO,CAACzD,MAAM,GAAG;IAC7E,CAAC,CAAC;IAEF,QAAQqD,YAAY,CAACQ,WAAW,CAAC,CAAC;MAChC,KAAK,KAAK;MACV,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;QACnB,OAAOC,oBAAoB,CAAC,CAAC;MAE/B,KAAK,MAAM;MACX,KAAK,mBAAmB;MACxB,KAAK,WAAW;MAChB,KAAK,MAAM;QACT,OAAOC,oBAAoB,CAAC,CAAC;MAE/B,KAAK,OAAO;MACZ,KAAK,SAAS;QACZ,OAAOC,mBAAmB,CAAC,CAAC;MAE9B;QACE;QACA,OAAOF,oBAAoB,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACR,QAAQ,CAACG,OAAO,IAAI,CAACxD,KAAK,CAAC2D,OAAO,CAACN,QAAQ,CAACG,OAAO,CAAC,IAAIH,QAAQ,CAACG,OAAO,CAACzD,MAAM,KAAK,CAAC,EAAE;MAC1F,oBACEjC,OAAA;QAAKkG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BnG,OAAA;UAAGkG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAEV;IAEA,oBACEvG,OAAA;MAAKkG,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBZ,QAAQ,CAACG,OAAO,CAACzC,GAAG,CAAC,CAACuD,MAAM,EAAErD,KAAK,KAAK;QACvC,MAAMsD,YAAY,GAAG/C,MAAM,CAACgD,YAAY,CAAC,EAAE,GAAGvD,KAAK,CAAC;QACpD,MAAMwD,UAAU,GAAG5F,OAAO,CAACF,eAAe,CAAC,KAAK2F,MAAM;QAEtD,oBACExG,OAAA;UAEE4G,OAAO,EAAEA,CAAA,KAAMlC,kBAAkB,CAAC8B,MAAM,CAAE;UAC1CN,SAAS,EAAG,wFACVS,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;UAAAR,QAAA,eAEHnG,OAAA;YAAKkG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCnG,OAAA;cAAKkG,SAAS,EAAG,2FACfS,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;cAAAR,QAAA,EACAM;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNvG,OAAA;cAAMkG,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACvD,OAAOK,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG/D,IAAI,CAACoE,SAAS,CAACL,MAAM;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC,GAnBDpD,KAAK;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBJ,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMP,oBAAoB,GAAGA,CAAA,KAAM;IACjC,oBACEhG,OAAA;MAAKkG,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBnG,OAAA;QAAKkG,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DnG,OAAA;UAAGkG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5EvG,OAAA;UAAGkG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACNvG,OAAA;QAAKkG,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBnG,OAAA;UACEwF,IAAI,EAAC,MAAM;UACXsB,KAAK,EAAE/F,OAAO,CAACF,eAAe,CAAC,IAAI,EAAG;UACtCkG,QAAQ,EAAGC,CAAC,IAAKtC,kBAAkB,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC,0BAA0B;UACtChB,SAAS,EAAC,mHAAmH;UAC7HiB,SAAS;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMN,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIV,QAAQ,CAACG,OAAO,IAAIxD,KAAK,CAAC2D,OAAO,CAACN,QAAQ,CAACG,OAAO,CAAC,IAAIH,QAAQ,CAACG,OAAO,CAACzD,MAAM,GAAG,CAAC,EAAE;MACtF,OAAO8D,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAOC,oBAAoB,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,IAAIzF,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKkG,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnG,OAAA;QAAKkG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnG,OAAA;UAAKkG,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGvG,OAAA;UAAGkG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC9F,IAAI,IAAI,CAACE,SAAS,CAACsB,MAAM,EAAE;IAC9B,oBACEjC,OAAA;MAAKkG,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnG,OAAA;QAAKkG,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFnG,OAAA;UAAKkG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnG,OAAA;YAAIkG,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFvG,OAAA;YAAGkG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/EvG,OAAA;YACE4G,OAAO,EAAEA,CAAA,KAAMxH,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD8F,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC5F,SAAS,CAACE,eAAe,CAAC,EAAE;IAC/B,oBACEb,OAAA;MAAKkG,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnG,OAAA;QAAKkG,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFnG,OAAA;UAAKkG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnG,OAAA;YAAIkG,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EvG,OAAA;YAAGkG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1EvG,OAAA;YACE4G,OAAO,EAAEA,CAAA,KAAMxH,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD8F,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMhB,QAAQ,GAAG5E,SAAS,CAACE,eAAe,CAAC;EAC3C,MAAMuG,cAAc,GAAGvG,eAAe,KAAKF,SAAS,CAACsB,MAAM,GAAG,CAAC;;EAE/D;EACA,IAAI,CAACsD,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,oBACEvF,OAAA;MAAKkG,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnG,OAAA;QAAKkG,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFnG,OAAA;UAAKkG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnG,OAAA;YAAIkG,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EvG,OAAA;YAAGkG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFvG,OAAA;YACE4G,OAAO,EAAEA,CAAA,KAAMxH,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD8F,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAIA,oBACEvG,OAAA;IAAKkG,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEnG,OAAA;MAAKkG,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DnG,OAAA;QAAKkG,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnG,OAAA;UAAKkG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnG,OAAA;YAAKkG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnG,OAAA;cACE4G,OAAO,EAAEA,CAAA,KAAMxH,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;cACxD8F,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eAE9DnG,OAAA,CAACN,WAAW;gBAACwG,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACTvG,OAAA;cAAAmG,QAAA,gBACEnG,OAAA;gBAAIkG,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAE1F,IAAI,CAACgD;cAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEvG,OAAA;gBAAGkG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAC1B,EAACtF,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACsB,MAAM;cAAA;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvG,OAAA;YAAKkG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtCnG,OAAA;cAAKkG,SAAS,EAAG,gDACfjF,QAAQ,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAC/C,EAAE;cAAAkF,QAAA,gBACDnG,OAAA,CAACP,OAAO;gBAACyG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BvG,OAAA;gBAAMkG,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEpB,UAAU,CAAC9D,QAAQ;cAAC;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvG,OAAA;UAAKkG,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBnG,OAAA;YAAKkG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDnG,OAAA;cACEkG,SAAS,EAAC,0DAA0D;cACpEmB,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAG,CAACzG,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACsB,MAAM,GAAI,GAAI;cAAG;YAAE;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvG,OAAA;MAAKkG,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCnG,OAAA;QAAKkG,SAAS,EAAC,uFAAuF;QAAAC,QAAA,gBAEpGnG,OAAA;UAAKkG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBnG,OAAA;YAAIkG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAClD,OAAOZ,QAAQ,CAAC9B,IAAI,KAAK,QAAQ,GAAG8B,QAAQ,CAAC9B,IAAI,GAAG;UAAU;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,EAEJhB,QAAQ,CAACgC,KAAK,iBACbvH,OAAA;YAAKkG,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7CnG,OAAA;cACEwH,GAAG,EAAEjC,QAAQ,CAACgC,KAAM;cACpBE,GAAG,EAAC,kBAAkB;cACtBvB,SAAS,EAAC,sDAAsD;cAChEmB,KAAK,EAAE;gBAAEK,SAAS,EAAE;cAAQ,CAAE;cAC9BC,OAAO,EAAGX,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACI,KAAK,CAACO,OAAO,GAAG,MAAM;gBAC/B;gBACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBAC9CF,QAAQ,CAAC3B,SAAS,GAAG,gCAAgC;gBACrD2B,QAAQ,CAACG,SAAS,GAAG,+BAA+B;gBACpDhB,CAAC,CAACC,MAAM,CAACgB,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC;cAC3C;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNvG,OAAA;UAAKkG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5Bd,mBAAmB,CAAC;QAAC;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGNvG,OAAA;UAAKkG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnG,OAAA;YACE4G,OAAO,EAAE9B,YAAa;YACtBqD,QAAQ,EAAEtH,eAAe,KAAK,CAAE;YAChCqF,SAAS,EAAG,gFACVrF,eAAe,KAAK,CAAC,GACjB,8CAA8C,GAC9C,6CACL,EAAE;YAAAsF,QAAA,gBAEHnG,OAAA,CAACN,WAAW;cAACwG,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERa,cAAc,gBACbpH,OAAA;YACE4G,OAAO,EAAEtE,gBAAiB;YAC1B4D,SAAS,EAAC,yHAAyH;YAAAC,QAAA,gBAEnInG,OAAA,CAACJ,OAAO;cAACsG,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETvG,OAAA;YACE4G,OAAO,EAAE/B,QAAS;YAClBqB,SAAS,EAAC,uHAAuH;YAAAC,QAAA,GAClI,MAEC,eAAAnG,OAAA,CAACL,YAAY;cAACuG,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrG,EAAA,CAjgBID,QAAQ;EAAA,QACGZ,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAA6I,EAAA,GAHxBnI,QAAQ;AAmgBd,eAAeA,QAAQ;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
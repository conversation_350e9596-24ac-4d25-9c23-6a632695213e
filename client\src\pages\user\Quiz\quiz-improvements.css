/* Quiz Improvements CSS - Enhanced Mobile Responsiveness and Scrolling */

/* Hide scrollbar for question navigation */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Enhanced smooth scrolling for quiz content */
.quiz-content-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
}

/* Better touch targets for mobile */
@media (max-width: 640px) {
  .quiz-option-mobile {
    min-height: 60px;
    touch-action: manipulation;
  }
  
  .quiz-nav-button-mobile {
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
  }
}

/* Enhanced image container for better responsive behavior */
.quiz-image-responsive {
  max-width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
}

/* Better spacing for fill-in-blank on mobile */
@media (max-width: 640px) {
  .fill-blank-container {
    padding: 1rem;
  }
  
  .fill-blank-textarea {
    min-height: 80px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Enhanced focus states for accessibility */
.quiz-element:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Better button states for touch devices */
.quiz-button {
  transition: all 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.quiz-button:active {
  transform: scale(0.98);
}

/* Improved loading states */
.quiz-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Better error message styling */
.quiz-error-message {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #f87171;
  border-radius: 8px;
  padding: 1rem;
  color: #dc2626;
  font-size: 0.875rem;
  text-align: center;
}

/* Enhanced progress indicator */
.quiz-progress-bar {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 9999px;
  height: 6px;
  transition: width 0.5s ease-in-out;
}

/* Better modal backdrop for mobile */
.quiz-modal-backdrop {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Improved typography for better readability */
.quiz-question-text {
  line-height: 1.6;
  word-wrap: break-word;
  hyphens: auto;
}

/* Enhanced answer option styling */
.quiz-answer-option {
  word-wrap: break-word;
  hyphens: auto;
  line-height: 1.5;
}

/* Better spacing for different screen sizes */
@media (min-width: 640px) {
  .quiz-container-sm {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .quiz-container-md {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .quiz-container-lg {
    padding: 2.5rem;
  }
}

/* Enhanced dark mode support */
@media (prefers-color-scheme: dark) {
  .quiz-dark-mode {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .quiz-dark-mode .quiz-option {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .quiz-dark-mode .quiz-option:hover {
    background-color: #4b5563;
  }
}

/* Better print styles */
@media print {
  .quiz-navigation,
  .quiz-timer,
  .quiz-progress {
    display: none !important;
  }
  
  .quiz-content {
    max-height: none !important;
    overflow: visible !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .quiz-option {
    border-width: 3px;
  }
  
  .quiz-button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .quiz-button,
  .quiz-option,
  .quiz-progress-bar {
    transition: none;
    animation: none;
  }
}

/* Better landscape orientation support on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .quiz-content-landscape {
    max-height: calc(100vh - 120px);
  }
  
  .quiz-navigation-landscape {
    padding: 0.5rem 1rem;
  }
}

/* Enhanced touch feedback */
@media (hover: none) and (pointer: coarse) {
  .quiz-option:active {
    background-color: #e5e7eb;
    transform: scale(0.98);
  }
  
  .quiz-button:active {
    transform: scale(0.95);
  }
}

/* Better safe area support for notched devices */
@supports (padding: max(0px)) {
  .quiz-container-safe {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }
  
  .quiz-navigation-safe {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Enhanced focus management for keyboard navigation */
.quiz-element:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Better text selection */
.quiz-question-text,
.quiz-answer-option {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.quiz-button,
.quiz-navigation {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

import React from 'react';
import { useSelector } from 'react-redux';

const AuthDebug = () => {
  const { user } = useSelector((state) => state.user);
  const token = localStorage.getItem('token');
  const userFromStorage = localStorage.getItem('user');

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      padding: '10px', 
      border: '1px solid #ccc',
      borderRadius: '5px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9999
    }}>
      <h4>Auth Debug Info</h4>
      <p><strong>Redux User:</strong> {user ? `${user.name} (${user.email})` : 'Not logged in'}</p>
      <p><strong>Token:</strong> {token ? 'Present' : 'Missing'}</p>
      <p><strong>User Storage:</strong> {userFromStorage ? 'Present' : 'Missing'}</p>
      {user && (
        <div>
          <p><strong>User ID:</strong> {user._id}</p>
          <p><strong>Level:</strong> {user.level}</p>
          <p><strong>Class:</strong> {user.class}</p>
          <p><strong>Admin:</strong> {user.isAdmin ? 'Yes' : 'No'}</p>
        </div>
      )}
    </div>
  );
};

export default AuthDebug;

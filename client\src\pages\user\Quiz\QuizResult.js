import React, { startTransition } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { TbTrophy, Tb<PERSON>lock, TbCheck, TbX, TbHome } from 'react-icons/tb';

const QuizResult = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  
  // Get result data from navigation state
  const resultData = location.state || {
    percentage: 0,
    correctAnswers: 0,
    totalQuestions: 0,
    timeTaken: 0,
    resultDetails: []
  };

  const { percentage, correctAnswers, totalQuestions, timeTaken } = resultData;
  const isPassed = percentage >= 60;

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleBackToQuizzes = () => {
    console.log('🏠 Navigating to quiz listing...');
    startTransition(() => {
      navigate('/quiz');
    });
  };

  const handleRetakeQuiz = () => {
    console.log('🔄 Retaking quiz with ID:', id);
    if (id) {
      startTransition(() => {
        navigate(`/quiz/${id}/play`);
      });
    } else {
      console.log('❌ No quiz ID available, going to quiz listing');
      startTransition(() => {
        navigate('/quiz');
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      {/* Debug Panel */}
      <div className="fixed top-4 right-4 bg-yellow-100 border border-yellow-300 rounded-lg p-3 text-xs max-w-xs">
        <p><strong>Quiz ID:</strong> {id || 'Not available'}</p>
        <p><strong>Result Data:</strong> {JSON.stringify(resultData, null, 2)}</p>
      </div>

      <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-4 ${
            isPassed ? 'bg-green-100' : 'bg-red-100'
          }`}>
            <TbTrophy className={`w-10 h-10 ${
              isPassed ? 'text-green-600' : 'text-red-600'
            }`} />
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Quiz Completed!
          </h1>
          
          <p className={`text-lg font-semibold ${
            isPassed ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPassed ? 'Congratulations! You Passed!' : 'Keep Practicing!'}
          </p>
        </div>

        {/* Score Display */}
        <div className="text-center mb-8">
          <div className={`inline-block px-8 py-4 rounded-2xl ${
            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'
          }`}>
            <div className={`text-5xl font-bold mb-2 ${
              isPassed ? 'text-green-600' : 'text-red-600'
            }`}>
              {percentage}%
            </div>
            <div className="text-gray-600">
              Your Score
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-gray-50 rounded-xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TbCheck className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{correctAnswers}</div>
            <div className="text-sm text-gray-600">Correct</div>
          </div>
          
          <div className="bg-gray-50 rounded-xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TbX className="w-6 h-6 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{totalQuestions - correctAnswers}</div>
            <div className="text-sm text-gray-600">Wrong</div>
          </div>
          
          <div className="bg-gray-50 rounded-xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TbClock className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{formatTime(timeTaken)}</div>
            <div className="text-sm text-gray-600">Time Taken</div>
          </div>
        </div>

        {/* Performance Message */}
        <div className="bg-blue-50 rounded-xl p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Performance Summary</h3>
          <p className="text-gray-700">
            {isPassed 
              ? `Excellent work! You scored ${percentage}% and answered ${correctAnswers} out of ${totalQuestions} questions correctly.`
              : `You scored ${percentage}%. Keep practicing to improve your score. You got ${correctAnswers} out of ${totalQuestions} questions correct.`
            }
          </p>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={(e) => {
              e.preventDefault();
              console.log('🔥 More Quizzes button clicked!');
              handleBackToQuizzes();
            }}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer"
            type="button"
          >
            <TbHome className="w-5 h-5" />
            More Quizzes
          </button>

          <button
            onClick={(e) => {
              e.preventDefault();
              console.log('🔥 Retake Quiz button clicked!');
              handleRetakeQuiz();
            }}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer"
            type="button"
          >
            <TbTrophy className="w-5 h-5" />
            Retake Quiz
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuizResult;

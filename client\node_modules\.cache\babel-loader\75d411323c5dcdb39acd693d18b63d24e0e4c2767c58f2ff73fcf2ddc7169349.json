{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbFlag } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\nimport './quiz-improvements.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        const response = await getExamById({\n          examId: id\n        });\n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        message.error('Failed to load quiz');\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id) {\n      loadQuizData();\n    }\n  }, [id, navigate]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = async () => {\n    try {\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          question: question._id,\n          selectedAnswer: userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: user._id,\n        correctAnswers: resultDetails.filter(r => r.isCorrect),\n        wrongAnswers: resultDetails.filter(r => !r.isCorrect),\n        percentage,\n        timeTaken\n      };\n      await addReport(reportData);\n\n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this);\n  }\n  if (!examData || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbX, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"Quiz Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz could not be loaded or has no questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = (currentQuestionIndex + 1) / questions.length * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900 truncate\",\n              children: examData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progress}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round(progress), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`,\n                children: currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'Multiple Choice' : 'Fill in the Blank'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), answers[currentQuestionIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1 text-white text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), \"Answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 sm:p-6 flex-1 overflow-y-auto\",\n            style: {\n              maxHeight: 'calc(100vh - 280px)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                children: currentQuestion.name || currentQuestion.question\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), currentQuestion.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentQuestion.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"w-full max-w-full mx-auto h-auto object-contain rounded-lg\",\n                    style: {\n                      maxHeight: '300px'\n                    },\n                    onError: e => {\n                      console.error('Image failed to load:', currentQuestion.imageUrl);\n                      e.target.style.display = 'none';\n                      const errorDiv = e.target.nextElementSibling;\n                      if (errorDiv) {\n                        errorDiv.style.display = 'flex';\n                      }\n                    },\n                    onLoad: () => {\n                      console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                        className: \"w-12 h-12 mx-auto mb-2 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 296,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: \"Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 297,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: \"Please check your internet connection\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: (currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ?\n              // Multiple Choice Questions - Enhanced for mobile\n              Object.entries(currentQuestion.options).map(([key, value]) => {\n                const isSelected = answers[currentQuestionIndex] === key;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(key),\n                  className: `w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                  whileHover: {\n                    scale: 1.01\n                  },\n                  whileTap: {\n                    scale: 0.99\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start gap-3 sm:gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 330,\n                        columnNumber: 43\n                      }, this) : key\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 25\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this);\n              }) :\n              /*#__PURE__*/\n              // Enhanced Fill-in-the-Blank Questions with better spacing\n              _jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-sm font-semibold text-gray-700\",\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: answers[currentQuestionIndex] || '',\n                  onChange: e => handleAnswerSelect(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\",\n                  rows: \"4\",\n                  style: {\n                    minHeight: '100px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-xs text-gray-500\",\n                  children: \"Tip: Take your time to write a clear and complete answer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, currentQuestionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestionIndex === 0,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${currentQuestionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\",\n            children: questions.map((_, index) => {\n              const isAnswered = answers[index] !== undefined && answers[index] !== '';\n              const isCurrent = index === currentQuestionIndex;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentQuestionIndex(index),\n                className: `w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${isCurrent ? 'bg-blue-600 text-white scale-110' : isAnswered ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'}`,\n                children: isAnswered && !isCurrent ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 23\n                }, this) : index + 1\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext,\n            className: \"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"Submit Quiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sm:hidden\",\n                  children: \"Submit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(TbFlag, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"sjPc4WX/o3i+A7ACKMAAP624uZg=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useDispatch", "useSelector", "message", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbFlag", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizPlay", "_s", "id", "navigate", "user", "state", "examData", "setExamData", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loading", "setLoading", "loadQuizData", "response", "examId", "success", "data", "duration", "Date", "error", "handleSubmitQuiz", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "answer", "goToNext", "length", "goToPrevious", "endTime", "timeTaken", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "_id", "<PERSON><PERSON><PERSON><PERSON>", "percentage", "round", "reportData", "exam", "filter", "r", "wrongAnswers", "totalQuestions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "progress", "isTimeWarning", "name", "div", "initial", "width", "animate", "transition", "mode", "opacity", "x", "exit", "type", "answerType", "style", "maxHeight", "imageUrl", "src", "alt", "onError", "e", "console", "target", "display", "errorDiv", "nextElement<PERSON><PERSON>ling", "onLoad", "log", "options", "Object", "entries", "key", "value", "isSelected", "button", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "minHeight", "disabled", "_", "isAnswered", "undefined", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Tb<PERSON>lock, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck, \n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbFlag\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\nimport './quiz-improvements.css';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        const response = await getExamById({ examId: id });\n        \n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        message.error('Failed to load quiz');\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id) {\n      loadQuizData();\n    }\n  }, [id, navigate]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = async () => {\n    try {\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      \n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        \n        return {\n          question: question._id,\n          selectedAnswer: userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      \n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: user._id,\n        correctAnswers: resultDetails.filter(r => r.isCorrect),\n        wrongAnswers: resultDetails.filter(r => !r.isCorrect),\n        percentage,\n        timeTaken\n      };\n\n      await addReport(reportData);\n      \n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbX className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-bold text-gray-900 mb-2\">Quiz Not Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz could not be loaded or has no questions.</p>\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\">\n      {/* Enhanced Header with better mobile support */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Info */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-xl font-bold text-gray-900 truncate\">\n                {examData.name}\n              </h1>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Question {currentQuestionIndex + 1} of {questions.length}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${\n              isTimeWarning \n                ? 'bg-red-100 text-red-700 animate-pulse' \n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-5 h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(progress)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Main Content with better mobile support */}\n      <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\"\n          >\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                  currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options'\n                    ? 'bg-blue-100 text-blue-800'\n                    : 'bg-green-100 text-green-800'\n                }`}>\n                  {currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' \n                    ? 'Multiple Choice' \n                    : 'Fill in the Blank'}\n                </div>\n                {answers[currentQuestionIndex] && (\n                  <div className=\"flex items-center gap-1 text-white text-sm font-medium\">\n                    <TbCheck className=\"w-4 h-4\" />\n                    Answered\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Question Content - Fixed scrolling and responsive design */}\n            <div className=\"p-4 sm:p-6 flex-1 overflow-y-auto\" style={{ maxHeight: 'calc(100vh - 280px)' }}>\n              <div className=\"mb-6\">\n                <h2 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                  {currentQuestion.name || currentQuestion.question}\n                </h2>\n\n                {/* Enhanced Image Display with better error handling */}\n                {currentQuestion.imageUrl && (\n                  <div className=\"mb-6\">\n                    <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\">\n                      <img\n                        src={currentQuestion.imageUrl}\n                        alt=\"Question diagram\"\n                        className=\"w-full max-w-full mx-auto h-auto object-contain rounded-lg\"\n                        style={{ maxHeight: '300px' }}\n                        onError={(e) => {\n                          console.error('Image failed to load:', currentQuestion.imageUrl);\n                          e.target.style.display = 'none';\n                          const errorDiv = e.target.nextElementSibling;\n                          if (errorDiv) {\n                            errorDiv.style.display = 'flex';\n                          }\n                        }}\n                        onLoad={() => {\n                          console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                        }}\n                      />\n                      <div className=\"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\">\n                        <div className=\"text-center\">\n                          <TbPhoto className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                          <p className=\"text-sm\">Image could not be loaded</p>\n                          <p className=\"text-xs text-gray-400 mt-1\">Please check your internet connection</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Enhanced Answer Options with better responsive design */}\n              <div className=\"space-y-3 sm:space-y-4\">\n                {(currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ? (\n                  // Multiple Choice Questions - Enhanced for mobile\n                  Object.entries(currentQuestion.options).map(([key, value]) => {\n                    const isSelected = answers[currentQuestionIndex] === key;\n                    return (\n                      <motion.button\n                        key={key}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 shadow-md'\n                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                        }`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <div className=\"flex items-start gap-3 sm:gap-4\">\n                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" /> : key}\n                          </div>\n                          <span className=\"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\">{value}</span>\n                        </div>\n                      </motion.button>\n                    );\n                  })\n                ) : (\n                  // Enhanced Fill-in-the-Blank Questions with better spacing\n                  <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\">\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                        <TbEdit className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                      </div>\n                      <label className=\"text-sm font-semibold text-gray-700\">\n                        Your Answer:\n                      </label>\n                    </div>\n                    <textarea\n                      value={answers[currentQuestionIndex] || ''}\n                      onChange={(e) => handleAnswerSelect(e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\"\n                      rows=\"4\"\n                      style={{ minHeight: '100px' }}\n                    />\n                    <div className=\"mt-2 text-xs text-gray-500\">\n                      Tip: Take your time to write a clear and complete answer\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Enhanced Navigation Footer - Mobile Responsive */}\n      <div className=\"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Previous Button - Mobile Optimized */}\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${\n                currentQuestionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n              <span className=\"sm:hidden\">Prev</span>\n            </button>\n\n            {/* Question Navigation Dots - Enhanced for mobile */}\n            <div className=\"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\">\n              {questions.map((_, index) => {\n                const isAnswered = answers[index] !== undefined && answers[index] !== '';\n                const isCurrent = index === currentQuestionIndex;\n                return (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentQuestionIndex(index)}\n                    className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${\n                      isCurrent\n                        ? 'bg-blue-600 text-white scale-110'\n                        : isAnswered\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-200 text-gray-600 hover:bg-gray-300'\n                    }`}\n                  >\n                    {isAnswered && !isCurrent ? (\n                      <TbCheck className=\"w-4 h-4\" />\n                    ) : (\n                      index + 1\n                    )}\n                  </button>\n                );\n              })}\n            </div>\n\n            {/* Next/Submit Button - Mobile Optimized */}\n            <button\n              onClick={currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext}\n              className=\"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\"\n            >\n              <span>\n                {currentQuestionIndex === questions.length - 1 ? (\n                  <>\n                    <span className=\"hidden sm:inline\">Submit Quiz</span>\n                    <span className=\"sm:hidden\">Submit</span>\n                  </>\n                ) : (\n                  'Next'\n                )}\n              </span>\n              {currentQuestionIndex === questions.length - 1 ? (\n                <TbFlag className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              ) : (\n                <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAO,kBAAkB;AACzB,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAC1B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwB;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChB,MAAME,QAAQ,GAAG,MAAM3B,WAAW,CAAC;UAAE4B,MAAM,EAAEpB;QAAG,CAAC,CAAC;QAElD,IAAImB,QAAQ,CAACE,OAAO,EAAE;UACpBhB,WAAW,CAACc,QAAQ,CAACG,IAAI,CAAC;UAC1Bf,YAAY,CAACY,QAAQ,CAACG,IAAI,CAAChB,SAAS,IAAI,EAAE,CAAC;UAC3CO,WAAW,CAAC,CAACM,QAAQ,CAACG,IAAI,CAACC,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC;UAChDR,YAAY,CAAC,IAAIS,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC,MAAM;UACL3C,OAAO,CAAC4C,KAAK,CAACN,QAAQ,CAACtC,OAAO,CAAC;UAC/BoB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACd5C,OAAO,CAAC4C,KAAK,CAAC,qBAAqB,CAAC;QACpCxB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,SAAS;QACRgB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIjB,EAAE,EAAE;MACNkB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAClB,EAAE,EAAEC,QAAQ,CAAC,CAAC;;EAElB;EACA1B,SAAS,CAAC,MAAM;IACd,IAAIqC,QAAQ,IAAI,CAAC,EAAE;MACjBc,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9Bf,WAAW,CAACgB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACf,QAAQ,EAAEc,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAMK,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrC7B,UAAU,CAACkB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACrB,oBAAoB,GAAGgC;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIjC,oBAAoB,GAAGF,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;MAC/CjC,uBAAuB,CAACoB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInC,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACoB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMkB,OAAO,GAAG,IAAIpB,IAAI,CAAC,CAAC;MAC1B,MAAMqB,SAAS,GAAGX,IAAI,CAACC,KAAK,CAAC,CAACS,OAAO,GAAG9B,SAAS,IAAI,IAAI,CAAC;;MAE1D;MACA,IAAIgC,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGzC,SAAS,CAAC0C,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGzC,OAAO,CAACwC,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLG,QAAQ,EAAEA,QAAQ,CAACK,GAAG;UACtBC,cAAc,EAAEJ,UAAU;UAC1BE,aAAa,EAAEJ,QAAQ,CAACI,aAAa;UACrCD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMI,UAAU,GAAGtB,IAAI,CAACuB,KAAK,CAAEX,cAAc,GAAGxC,SAAS,CAACoC,MAAM,GAAI,GAAG,CAAC;;MAExE;MACA,MAAMgB,UAAU,GAAG;QACjBC,IAAI,EAAE3D,EAAE;QACRE,IAAI,EAAEA,IAAI,CAACoD,GAAG;QACdR,cAAc,EAAEC,aAAa,CAACa,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACT,SAAS,CAAC;QACtDU,YAAY,EAAEf,aAAa,CAACa,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACT,SAAS,CAAC;QACrDI,UAAU;QACVX;MACF,CAAC;MAED,MAAMpD,SAAS,CAACiE,UAAU,CAAC;;MAE3B;MACAzD,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;QAC7BG,KAAK,EAAE;UACLqD,UAAU;UACVV,cAAc;UACdiB,cAAc,EAAEzD,SAAS,CAACoC,MAAM;UAChCG,SAAS;UACTE;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;EAED,IAAIT,OAAO,EAAE;IACX,oBACErB,OAAA;MAAKqE,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGtE,OAAA;QAAKqE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtE,OAAA;UAAKqE,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG1E,OAAA;UAAGqE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACjE,QAAQ,IAAIE,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE/C,OAAA;MAAKqE,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGtE,OAAA;QAAKqE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtE,OAAA,CAACP,GAAG;UAAC4E,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvD1E,OAAA;UAAIqE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E1E,OAAA;UAAGqE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxF1E,OAAA;UACE2E,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,YAAY,CAAE;UACtC+D,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAGjE,SAAS,CAACE,oBAAoB,CAAC;EACvD,MAAMgE,QAAQ,GAAI,CAAChE,oBAAoB,GAAG,CAAC,IAAIF,SAAS,CAACoC,MAAM,GAAI,GAAG;EACtE,MAAM+B,aAAa,GAAG7D,QAAQ,IAAI,GAAG,CAAC,CAAC;;EAEvC,oBACEjB,OAAA;IAAKqE,SAAS,EAAC,+EAA+E;IAAAC,QAAA,gBAE5FtE,OAAA;MAAKqE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EtE,OAAA;QAAKqE,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DtE,OAAA;UAAKqE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhDtE,OAAA;YAAKqE,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBtE,OAAA;cAAIqE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrD7D,QAAQ,CAACsE;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACL1E,OAAA;cAAGqE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,EAACzD,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACoC,MAAM;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN1E,OAAA;YAAKqE,SAAS,EAAG,qFACfS,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAAR,QAAA,gBACDtE,OAAA,CAACX,OAAO;cAACgF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B1E,OAAA;cAAAsE,QAAA,EAAOlC,UAAU,CAACnB,QAAQ;YAAC;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1E,OAAA;UAAKqE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBtE,OAAA;YAAKqE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDtE,OAAA,CAACb,MAAM,CAAC6F,GAAG;cACTX,SAAS,EAAC,+DAA+D;cACzEY,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAEL,QAAS;cAAG,CAAE;cACnCO,UAAU,EAAE;gBAAExD,QAAQ,EAAE;cAAI;YAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1E,OAAA;YAAKqE,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DtE,OAAA;cAAAsE,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrB1E,OAAA;cAAAsE,QAAA,GAAO/B,IAAI,CAACuB,KAAK,CAACe,QAAQ,CAAC,EAAC,GAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChHtE,OAAA,CAACZ,eAAe;QAACiG,IAAI,EAAC,MAAM;QAAAf,QAAA,eAC1BtE,OAAA,CAACb,MAAM,CAAC6F,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BH,UAAU,EAAE;YAAExD,QAAQ,EAAE;UAAI,CAAE;UAC9ByC,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAGxGtE,OAAA;YAAKqE,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEtE,OAAA;cAAKqE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtE,OAAA;gBAAKqE,SAAS,EAAG,gDACfO,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,GACtE,2BAA2B,GAC3B,6BACL,EAAE;gBAAApB,QAAA,EACAM,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,GACvE,iBAAiB,GACjB;cAAmB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EACL3D,OAAO,CAACF,oBAAoB,CAAC,iBAC5Bb,OAAA;gBAAKqE,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrEtE,OAAA,CAACR,OAAO;kBAAC6E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1E,OAAA;YAAKqE,SAAS,EAAC,mCAAmC;YAACsB,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAsB,CAAE;YAAAtB,QAAA,gBAC7FtE,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAIqE,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAChFM,eAAe,CAACG,IAAI,IAAIH,eAAe,CAACtB;cAAQ;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,EAGJE,eAAe,CAACiB,QAAQ,iBACvB7F,OAAA;gBAAKqE,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBtE,OAAA;kBAAKqE,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,gBACxFtE,OAAA;oBACE8F,GAAG,EAAElB,eAAe,CAACiB,QAAS;oBAC9BE,GAAG,EAAC,kBAAkB;oBACtB1B,SAAS,EAAC,4DAA4D;oBACtEsB,KAAK,EAAE;sBAAEC,SAAS,EAAE;oBAAQ,CAAE;oBAC9BI,OAAO,EAAGC,CAAC,IAAK;sBACdC,OAAO,CAACpE,KAAK,CAAC,uBAAuB,EAAE8C,eAAe,CAACiB,QAAQ,CAAC;sBAChEI,CAAC,CAACE,MAAM,CAACR,KAAK,CAACS,OAAO,GAAG,MAAM;sBAC/B,MAAMC,QAAQ,GAAGJ,CAAC,CAACE,MAAM,CAACG,kBAAkB;sBAC5C,IAAID,QAAQ,EAAE;wBACZA,QAAQ,CAACV,KAAK,CAACS,OAAO,GAAG,MAAM;sBACjC;oBACF,CAAE;oBACFG,MAAM,EAAEA,CAAA,KAAM;sBACZL,OAAO,CAACM,GAAG,CAAC,4BAA4B,EAAE5B,eAAe,CAACiB,QAAQ,CAAC;oBACrE;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF1E,OAAA;oBAAKqE,SAAS,EAAC,8EAA8E;oBAAAC,QAAA,eAC3FtE,OAAA;sBAAKqE,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BtE,OAAA,CAACN,OAAO;wBAAC2E,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5D1E,OAAA;wBAAGqE,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACpD1E,OAAA;wBAAGqE,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAC;sBAAqC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN1E,OAAA;cAAKqE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC,CAACM,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,KAAKd,eAAe,CAAC6B,OAAO;cACtG;cACAC,MAAM,CAACC,OAAO,CAAC/B,eAAe,CAAC6B,OAAO,CAAC,CAACpD,GAAG,CAAC,CAAC,CAACuD,GAAG,EAAEC,KAAK,CAAC,KAAK;gBAC5D,MAAMC,UAAU,GAAG/F,OAAO,CAACF,oBAAoB,CAAC,KAAK+F,GAAG;gBACxD,oBACE5G,OAAA,CAACb,MAAM,CAAC4H,MAAM;kBAEZpC,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAACgE,GAAG,CAAE;kBACvCvC,SAAS,EAAG,+EACVyC,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;kBACHE,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAA3C,QAAA,eAE1BtE,OAAA;oBAAKqE,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CtE,OAAA;sBAAKqE,SAAS,EAAG,0FACfyC,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;sBAAAxC,QAAA,EACAwC,UAAU,gBAAG9G,OAAA,CAACR,OAAO;wBAAC6E,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAGkC;oBAAG;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN1E,OAAA;sBAAMqE,SAAS,EAAC,gEAAgE;sBAAAC,QAAA,EAAEuC;oBAAK;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F;gBAAC,GAnBDkC,GAAG;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBK,CAAC;cAEpB,CAAC,CAAC;cAAA;cAEF;cACA1E,OAAA;gBAAKqE,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,gBAC3GtE,OAAA;kBAAKqE,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3CtE,OAAA;oBAAKqE,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjFtE,OAAA,CAACL,MAAM;sBAAC0E,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACN1E,OAAA;oBAAOqE,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEvD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1E,OAAA;kBACE6G,KAAK,EAAE9F,OAAO,CAACF,oBAAoB,CAAC,IAAI,EAAG;kBAC3CsG,QAAQ,EAAGlB,CAAC,IAAKrD,kBAAkB,CAACqD,CAAC,CAACE,MAAM,CAACU,KAAK,CAAE;kBACpDO,WAAW,EAAC,0BAA0B;kBACtC/C,SAAS,EAAC,8KAA8K;kBACxLgD,IAAI,EAAC,GAAG;kBACR1B,KAAK,EAAE;oBAAE2B,SAAS,EAAE;kBAAQ;gBAAE;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACF1E,OAAA;kBAAKqE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA5HD7D,oBAAoB;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6Hf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpHtE,OAAA;QAAKqE,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFtE,OAAA;UAAKqE,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/DtE,OAAA;YACE2E,OAAO,EAAE3B,YAAa;YACtBuE,QAAQ,EAAE1G,oBAAoB,KAAK,CAAE;YACrCwD,SAAS,EAAG,2HACVxD,oBAAoB,KAAK,CAAC,GACtB,8CAA8C,GAC9C,6DACL,EAAE;YAAAyD,QAAA,gBAEHtE,OAAA,CAACV,WAAW;cAAC+E,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD1E,OAAA;cAAMqE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD1E,OAAA;cAAMqE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAGT1E,OAAA;YAAKqE,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAClG3D,SAAS,CAAC0C,GAAG,CAAC,CAACmE,CAAC,EAAEjE,KAAK,KAAK;cAC3B,MAAMkE,UAAU,GAAG1G,OAAO,CAACwC,KAAK,CAAC,KAAKmE,SAAS,IAAI3G,OAAO,CAACwC,KAAK,CAAC,KAAK,EAAE;cACxE,MAAMoE,SAAS,GAAGpE,KAAK,KAAK1C,oBAAoB;cAChD,oBACEb,OAAA;gBAEE2E,OAAO,EAAEA,CAAA,KAAM7D,uBAAuB,CAACyC,KAAK,CAAE;gBAC9Cc,SAAS,EAAG,sHACVsD,SAAS,GACL,kCAAkC,GAClCF,UAAU,GACV,yBAAyB,GACzB,6CACL,EAAE;gBAAAnD,QAAA,EAEFmD,UAAU,IAAI,CAACE,SAAS,gBACvB3H,OAAA,CAACR,OAAO;kBAAC6E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAE/BnB,KAAK,GAAG;cACT,GAdIA,KAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeJ,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1E,OAAA;YACE2E,OAAO,EAAE9D,oBAAoB,KAAKF,SAAS,CAACoC,MAAM,GAAG,CAAC,GAAGhB,gBAAgB,GAAGe,QAAS;YACrFuB,SAAS,EAAC,kLAAkL;YAAAC,QAAA,gBAE5LtE,OAAA;cAAAsE,QAAA,EACGzD,oBAAoB,KAAKF,SAAS,CAACoC,MAAM,GAAG,CAAC,gBAC5C/C,OAAA,CAAAE,SAAA;gBAAAoE,QAAA,gBACEtE,OAAA;kBAAMqE,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrD1E,OAAA;kBAAMqE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACzC,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACN7D,oBAAoB,KAAKF,SAAS,CAACoC,MAAM,GAAG,CAAC,gBAC5C/C,OAAA,CAACJ,MAAM;cAACyE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5C1E,OAAA,CAACT,YAAY;cAAC8E,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CAlaID,QAAQ;EAAA,QACGrB,SAAS,EACPC,WAAW,EACXE,WAAW;AAAA;AAAA2I,EAAA,GAHxBzH,QAAQ;AAoad,eAAeA,QAAQ;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
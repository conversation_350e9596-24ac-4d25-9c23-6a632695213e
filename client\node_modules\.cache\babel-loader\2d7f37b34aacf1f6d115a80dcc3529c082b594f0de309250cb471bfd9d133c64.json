{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlayDebug.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlayDebug = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n        const response = await getExamById(id);\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60); // Convert minutes to seconds\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      // Get user data from Redux or localStorage\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail'; // 60% pass mark\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          // Add score field for backend compatibility\n          verdict: verdict,\n          // Add verdict field for backend compatibility\n          timeTaken,\n          points: correctAnswers * 10 // Add points calculation (10 points per correct answer)\n        }\n      };\n\n      console.log('Submitting quiz result:', reportData);\n      const response = await addReport(reportData);\n      console.log('Quiz submitted successfully:', response);\n\n      // Navigate to results\n      startTransition(() => {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails\n          }\n        });\n      });\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-red-600 mb-4\",\n            children: \"Quiz Error\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this);\n  }\n  if (!quiz || !questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => startTransition(() => navigate('/quiz')),\n              className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-6 h-6 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: quiz.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Question \", currentQuestion + 1, \" of \", questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 px-4 py-2 rounded-lg ${timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${(currentQuestion + 1) / questions.length * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto p-6\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          x: 20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        exit: {\n          opacity: 0,\n          x: -20\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-4\",\n            children: currentQ.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), currentQ.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQ.image,\n              alt: \"Question\",\n              className: \"max-w-full h-auto rounded-lg shadow-md\",\n              onError: e => {\n                e.target.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 mb-8\",\n          children: currentQ.options.map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n            const isSelected = answers[currentQuestion] === option;\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: () => handleAnswerSelect(option),\n              className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full flex items-center justify-center font-semibold ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestion === 0,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${currentQuestion === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSubmitQuiz,\n            className: \"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), \"Submit Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)]\n      }, currentQuestion, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 278,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlayDebug, \"xkT6k+3XSP5s46O0Vr7FI/6InA8=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlayDebug;\nexport default QuizPlayDebug;\nvar _c;\n$RefreshReg$(_c, \"QuizPlayDebug\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "message", "motion", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "QuizPlayDebug", "_s", "id", "navigate", "user", "state", "loading", "setLoading", "error", "setError", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "console", "log", "_id", "token", "localStorage", "getItem", "response", "success", "data", "length", "Array", "fill", "duration", "Date", "handleSubmitQuiz", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "percentage", "round", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "points", "totalQuestions", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQ", "isLastQuestion", "name", "style", "width", "div", "initial", "opacity", "x", "animate", "exit", "transition", "image", "src", "alt", "onError", "e", "target", "display", "options", "option", "optionLetter", "String", "fromCharCode", "isSelected", "button", "whileHover", "scale", "whileTap", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlayDebug.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion } from 'framer-motion';\nimport {\n  Tb<PERSON>lock,\n  TbArrowLeft,\n  TbArrowRight,\n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\nconst QuizPlayDebug = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById(id);\n        console.log('Quiz API response:', response);\n\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60); // Convert minutes to seconds\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      // Get user data from Redux or localStorage\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail'; // 60% pass mark\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage, // Add score field for backend compatibility\n          verdict: verdict, // Add verdict field for backend compatibility\n          timeTaken,\n          points: correctAnswers * 10 // Add points calculation (10 points per correct answer)\n        }\n      };\n\n      console.log('Submitting quiz result:', reportData);\n\n      const response = await addReport(reportData);\n      console.log('Quiz submitted successfully:', response);\n\n      // Navigate to results\n      startTransition(() => {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails\n          }\n        });\n      });\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-red-600 mb-4\">Quiz Error</h2>\n            <p className=\"text-gray-600 mb-4\">{error}</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <button\n                onClick={() => startTransition(() => navigate('/quiz'))}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <TbArrowLeft className=\"w-6 h-6 text-gray-600\" />\n              </button>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{quiz.name}</h1>\n                <p className=\"text-sm text-gray-600\">\n                  Question {currentQuestion + 1} of {questions.length}\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-4\">\n              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${\n                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}>\n                <TbClock className=\"w-5 h-5\" />\n                <span className=\"font-semibold\">{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Progress bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto p-6\">\n        <motion.div\n          key={currentQuestion}\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          exit={{ opacity: 0, x: -20 }}\n          transition={{ duration: 0.3 }}\n          className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8\"\n        >\n          {/* Question */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {currentQ.name}\n            </h2>\n\n            {currentQ.image && (\n              <div className=\"mb-6\">\n                <img\n                  src={currentQ.image}\n                  alt=\"Question\"\n                  className=\"max-w-full h-auto rounded-lg shadow-md\"\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Options */}\n          <div className=\"space-y-4 mb-8\">\n            {currentQ.options.map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n              const isSelected = answers[currentQuestion] === option;\n\n              return (\n                <motion.button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <div className=\"flex items-center gap-4\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center font-semibold ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg\">{option}</span>\n                  </div>\n                </motion.button>\n              );\n            })}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex justify-between items-center\">\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                className=\"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\"\n              >\n                <TbCheck className=\"w-5 h-5\" />\n                Submit Quiz\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbArrowRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlayDebug;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAG,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC1B,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAK,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFf,UAAU,CAAC,IAAI,CAAC;QAChBgB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEtB,EAAE,CAAC;QAExC,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACqB,GAAG,EAAE;UACtB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAI,CAACF,KAAK,EAAE;YACVH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnDlC,OAAO,CAACkB,KAAK,CAAC,gCAAgC,CAAC;YAC/CtB,eAAe,CAAC,MAAM;cACpBiB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;QAEA,MAAM0B,QAAQ,GAAG,MAAMjC,WAAW,CAACM,EAAE,CAAC;QACtCqB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACD,QAAQ,CAACE,IAAI,EAAE;YAClBzC,OAAO,CAACkB,KAAK,CAAC,qBAAqB,CAAC;YACpCtB,eAAe,CAAC,MAAM;cACpBiB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAAC0B,QAAQ,CAACE,IAAI,CAACnB,SAAS,IAAIiB,QAAQ,CAACE,IAAI,CAACnB,SAAS,CAACoB,MAAM,KAAK,CAAC,EAAE;YACpE1C,OAAO,CAACkB,KAAK,CAAC,sCAAsC,CAAC;YACrDtB,eAAe,CAAC,MAAM;cACpBiB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAQ,OAAO,CAACkB,QAAQ,CAACE,IAAI,CAAC;UACtBlB,YAAY,CAACgB,QAAQ,CAACE,IAAI,CAACnB,SAAS,CAAC;UACrCK,UAAU,CAAC,IAAIgB,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACnB,SAAS,CAACoB,MAAM,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;UAC9Df,WAAW,CAACU,QAAQ,CAACE,IAAI,CAACI,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;UAC1Cd,YAAY,CAAC,IAAIe,IAAI,CAAC,CAAC,CAAC;UACxBb,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEK,QAAQ,CAACE,IAAI,CAAC;QACzD,CAAC,MAAM;UACLR,OAAO,CAACf,KAAK,CAAC,iBAAiB,EAAEqB,QAAQ,CAACvC,OAAO,CAAC;UAClDA,OAAO,CAACkB,KAAK,CAACqB,QAAQ,CAACvC,OAAO,IAAI,qBAAqB,CAAC;UACxDJ,eAAe,CAAC,MAAM;YACpBiB,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOK,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3ClB,OAAO,CAACkB,KAAK,CAAC,wCAAwC,CAAC;QACvDtB,eAAe,CAAC,MAAM;UACpBiB,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIL,EAAE,IAAIE,IAAI,EAAE;MACdkB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACpB,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMiC,gBAAgB,GAAGpD,WAAW,CAAC,YAAY;IAC/C,IAAI;MACF;MACA,IAAIqD,WAAW,GAAGlC,IAAI;MACtB,IAAI,CAACkC,WAAW,IAAI,CAACA,WAAW,CAACb,GAAG,EAAE;QACpC,MAAMc,UAAU,GAAGZ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIW,UAAU,EAAE;UACd,IAAI;YACFD,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;UACtC,CAAC,CAAC,OAAO/B,KAAK,EAAE;YACde,OAAO,CAACf,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvDtB,eAAe,CAAC,MAAM;cACpBiB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACmC,WAAW,IAAI,CAACA,WAAW,CAACb,GAAG,EAAE;QACpCnC,OAAO,CAACkB,KAAK,CAAC,2CAA2C,CAAC;QAC1DtB,eAAe,CAAC,MAAM;UACpBiB,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAMuC,OAAO,GAAG,IAAIN,IAAI,CAAC,CAAC;MAC1B,MAAMO,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGtB,SAAS,IAAI,IAAI,CAAC;;MAE1D;MACA,IAAI0B,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGnC,SAAS,CAACoC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGnC,OAAO,CAACkC,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLG,QAAQ,EAAEA,QAAQ,CAACxB,GAAG;UACtB0B,UAAU;UACVE,aAAa,EAAEJ,QAAQ,CAACI,aAAa;UACrCD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAME,UAAU,GAAGV,IAAI,CAACW,KAAK,CAAET,cAAc,GAAGlC,SAAS,CAACoB,MAAM,GAAI,GAAG,CAAC;MACxE,MAAMwB,OAAO,GAAGF,UAAU,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;;MAEpD;MACA,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAExD,EAAE;QACRE,IAAI,EAAEkC,WAAW,CAACb,GAAG;QACrBkC,MAAM,EAAE;UACNb,cAAc;UACdc,YAAY,EAAEhD,SAAS,CAACoB,MAAM,GAAGc,cAAc;UAC/CQ,UAAU;UACVO,KAAK,EAAEP,UAAU;UAAE;UACnBE,OAAO,EAAEA,OAAO;UAAE;UAClBb,SAAS;UACTmB,MAAM,EAAEhB,cAAc,GAAG,EAAE,CAAC;QAC9B;MACF,CAAC;;MAEDvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEiC,UAAU,CAAC;MAElD,MAAM5B,QAAQ,GAAG,MAAMhC,SAAS,CAAC4D,UAAU,CAAC;MAC5ClC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,QAAQ,CAAC;;MAErD;MACA3C,eAAe,CAAC,MAAM;QACpBiB,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAC7BG,KAAK,EAAE;YACLiD,UAAU;YACVR,cAAc;YACdiB,cAAc,EAAEnD,SAAS,CAACoB,MAAM;YAChCW,SAAS;YACTI;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9ClB,OAAO,CAACkB,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC,EAAE,CAACY,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEd,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEvD;EACApB,SAAS,CAAC,MAAM;IACd,IAAIkC,QAAQ,IAAI,CAAC,EAAE;MACjBmB,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAM2B,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B9C,WAAW,CAAC+C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAAC9C,QAAQ,EAAEmB,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAM+B,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC,GAAGtD,OAAO,CAAC;IAC/BsD,UAAU,CAACxD,eAAe,CAAC,GAAGuD,MAAM;IACpCpD,UAAU,CAACqD,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIzD,eAAe,GAAGF,SAAS,CAACoB,MAAM,GAAG,CAAC,EAAE;MAC1CjB,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1D,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM2D,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAG/B,IAAI,CAACC,KAAK,CAAC6B,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,IAAIxE,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKgF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGjF,OAAA;QAAKgF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjF,OAAA;UAAKgF,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGrF,OAAA;UAAGgF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI5E,KAAK,EAAE;IACT,oBACET,OAAA;MAAKgF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGjF,OAAA;QAAKgF,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFjF,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjF,OAAA;YAAIgF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnErF,OAAA;YAAGgF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAExE;UAAK;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CrF,OAAA;YACEsF,OAAO,EAAEA,CAAA,KAAMnG,eAAe,CAAC,MAAMiB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD4E,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAC1E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC1E,IAAI,IAAI,CAACE,SAAS,CAACoB,MAAM,EAAE;IAC9B,oBACEjC,OAAA;MAAKgF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGjF,OAAA;QAAKgF,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFjF,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjF,OAAA;YAAIgF,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFrF,OAAA;YAAGgF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/ErF,OAAA;YACEsF,OAAO,EAAEA,CAAA,KAAMnG,eAAe,CAAC,MAAMiB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD4E,SAAS,EAAC,oEAAoE;YAAAC,QAAA,EAC/E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,QAAQ,GAAG1E,SAAS,CAACE,eAAe,CAAC;EAC3C,MAAMyE,cAAc,GAAGzE,eAAe,KAAKF,SAAS,CAACoB,MAAM,GAAG,CAAC;EAE/D,oBACEjC,OAAA;IAAKgF,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEjF,OAAA;MAAKgF,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DjF,OAAA;QAAKgF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CjF,OAAA;UAAKgF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDjF,OAAA;YAAKgF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCjF,OAAA;cACEsF,OAAO,EAAEA,CAAA,KAAMnG,eAAe,CAAC,MAAMiB,QAAQ,CAAC,OAAO,CAAC,CAAE;cACxD4E,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eAE9DjF,OAAA,CAACN,WAAW;gBAACsF,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACTrF,OAAA;cAAAiF,QAAA,gBACEjF,OAAA;gBAAIgF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAEtE,IAAI,CAAC8E;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChErF,OAAA;gBAAGgF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAC1B,EAAClE,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACoB,MAAM;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrF,OAAA;YAAKgF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtCjF,OAAA;cAAKgF,SAAS,EAAG,gDACf7D,QAAQ,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAC/C,EAAE;cAAA8D,QAAA,gBACDjF,OAAA,CAACP,OAAO;gBAACuF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BrF,OAAA;gBAAMgF,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEP,UAAU,CAACvD,QAAQ;cAAC;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrF,OAAA;UAAKgF,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBjF,OAAA;YAAKgF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDjF,OAAA;cACEgF,SAAS,EAAC,0DAA0D;cACpEU,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAG,CAAC5E,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACoB,MAAM,GAAI,GAAI;cAAG;YAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrF,OAAA;MAAKgF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCjF,OAAA,CAACR,MAAM,CAACoG,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAC7BG,UAAU,EAAE;UAAE9D,QAAQ,EAAE;QAAI,CAAE;QAC9B4C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBAGrEjF,OAAA;UAAKgF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjF,OAAA;YAAIgF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAClDM,QAAQ,CAACE;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EAEJE,QAAQ,CAACY,KAAK,iBACbnG,OAAA;YAAKgF,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBjF,OAAA;cACEoG,GAAG,EAAEb,QAAQ,CAACY,KAAM;cACpBE,GAAG,EAAC,UAAU;cACdrB,SAAS,EAAC,wCAAwC;cAClDsB,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACe,OAAO,GAAG,MAAM;cACjC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrF,OAAA;UAAKgF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BM,QAAQ,CAACmB,OAAO,CAACzD,GAAG,CAAC,CAAC0D,MAAM,EAAExD,KAAK,KAAK;YACvC,MAAMyD,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG3D,KAAK,CAAC,CAAC,CAAC;YACtD,MAAM4D,UAAU,GAAG9F,OAAO,CAACF,eAAe,CAAC,KAAK4F,MAAM;YAEtD,oBACE3G,OAAA,CAACR,MAAM,CAACwH,MAAM;cAEZ1B,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAACsC,MAAM,CAAE;cAC1C3B,SAAS,EAAG,wEACV+B,UAAU,GACN,0CAA0C,GAC1C,iEACL,EAAE;cACHE,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAjC,QAAA,eAE1BjF,OAAA;gBAAKgF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCjF,OAAA;kBAAKgF,SAAS,EAAG,uEACf+B,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;kBAAA9B,QAAA,EACA2B;gBAAY;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNrF,OAAA;kBAAMgF,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAE0B;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC,GAnBDlC,KAAK;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBG,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrF,OAAA;UAAKgF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDjF,OAAA;YACEsF,OAAO,EAAEb,YAAa;YACtB2C,QAAQ,EAAErG,eAAe,KAAK,CAAE;YAChCiE,SAAS,EAAG,gFACVjE,eAAe,KAAK,CAAC,GACjB,8CAA8C,GAC9C,6CACL,EAAE;YAAAkE,QAAA,gBAEHjF,OAAA,CAACN,WAAW;cAACsF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERG,cAAc,gBACbxF,OAAA;YACEsF,OAAO,EAAEhD,gBAAiB;YAC1B0C,SAAS,EAAC,yHAAyH;YAAAC,QAAA,gBAEnIjF,OAAA,CAACJ,OAAO;cAACoF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETrF,OAAA;YACEsF,OAAO,EAAEd,QAAS;YAClBQ,SAAS,EAAC,uHAAuH;YAAAC,QAAA,GAClI,MAEC,eAAAjF,OAAA,CAACL,YAAY;cAACqF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GA5FDtE,eAAe;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6FV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnF,EAAA,CArZID,aAAa;EAAA,QACFb,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAA+H,EAAA,GAHxBpH,aAAa;AAuZnB,eAAeA,aAAa;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
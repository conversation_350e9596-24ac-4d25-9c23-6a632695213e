{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbSearch, TbFilter, TbClock, TbQuestionMark, TbTrophy, TbPlayerPlay, TbBrain, TbTarget, TbCheck, TbX, TbStar, TbHome, TbBolt, TbRefresh } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReportsByUser } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport './animations.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const [filteredExams, setFilteredExams] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [userResults, setUserResults] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [lastRefresh, setLastRefresh] = useState(null);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getUserResults = useCallback(async () => {\n    try {\n      if (!(user !== null && user !== void 0 && user._id)) return;\n      const response = await getAllReportsByUser({\n        userId: user._id\n      });\n      if (response.success) {\n        const resultsMap = {};\n        response.data.forEach(report => {\n          var _report$exam;\n          const examId = (_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam._id;\n          if (!examId || !report.result) return;\n\n          // Extract data from the result object\n          const result = report.result;\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\n            resultsMap[examId] = {\n              verdict: result.verdict,\n              percentage: result.percentage,\n              correctAnswers: result.correctAnswers,\n              wrongAnswers: result.wrongAnswers,\n              totalQuestions: result.totalQuestions,\n              obtainedMarks: result.obtainedMarks,\n              totalMarks: result.totalMarks,\n              score: result.score,\n              points: result.points,\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\n              timeTaken: report.timeTaken,\n              completedAt: report.createdAt\n            };\n          }\n        });\n        setUserResults(resultsMap);\n      }\n    } catch (error) {\n      console.error('Error fetching user results:', error);\n    }\n  }, [user === null || user === void 0 ? void 0 : user._id]);\n\n  // Define getExams function outside useEffect so it can be called from other functions\n  const getExams = useCallback(async (isRefresh = false) => {\n    try {\n      // Safety check: ensure user exists before proceeding\n      if (!user) {\n        console.log(\"User not loaded yet, skipping exam fetch\");\n        return;\n      }\n\n      // Check cache first (unless refreshing)\n      if (!isRefresh) {\n        const cachedExams = localStorage.getItem('user_exams_cache');\n        const cacheTime = localStorage.getItem('user_exams_cache_time');\n        const now = Date.now();\n\n        // Use cache if less than 3 minutes old\n        if (cachedExams && cacheTime && now - parseInt(cacheTime) < 180000) {\n          const cached = JSON.parse(cachedExams);\n          setExams(cached);\n          setLastRefresh(new Date(parseInt(cacheTime)));\n          if (user !== null && user !== void 0 && user.class) {\n            setSelectedClass(String(user.class));\n          }\n          setLoading(false);\n          return;\n        }\n      }\n      if (isRefresh) {\n        setRefreshing(true);\n      } else {\n        dispatch(ShowLoading());\n      }\n      const response = await getAllExams();\n      if (isRefresh) {\n        setRefreshing(false);\n      } else {\n        dispatch(HideLoading());\n      }\n      if (response.success) {\n        // Filter exams by user's level with proper null checks\n        const userLevelExams = response.data.filter(exam => {\n          if (!exam.level || !user || !user.level) return false;\n          return exam.level.toLowerCase() === user.level.toLowerCase();\n        });\n        const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n        setExams(sortedExams);\n        setLastRefresh(new Date());\n\n        // Cache the exams data\n        localStorage.setItem('user_exams_cache', JSON.stringify(sortedExams));\n        localStorage.setItem('user_exams_cache_time', Date.now().toString());\n\n        // Set default class filter to user's class\n        if (user !== null && user !== void 0 && user.class) {\n          setSelectedClass(String(user.class));\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      if (isRefresh) {\n        setRefreshing(false);\n      } else {\n        dispatch(HideLoading());\n      }\n      message.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [dispatch, user]);\n  useEffect(() => {\n    getExams(false); // Initial load\n    getUserResults();\n  }, [getExams, getUserResults]);\n\n  // Real-time updates for quiz completion and new exams\n  useEffect(() => {\n    // Listen for real-time updates from quiz completion\n    const handleRankingUpdate = () => {\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\n      getUserResults(); // Refresh user results to show updated XP\n    };\n\n    // Listen for new exam creation events\n    const handleNewExam = () => {\n      console.log('🆕 New exam created - refreshing exam list...');\n      if (user) {\n        getExams(true); // Use refresh mode\n        getUserResults();\n      }\n    };\n\n    // Listen for window focus to refresh data when returning from quiz\n    const handleWindowFocus = () => {\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\n      getUserResults();\n      // Also refresh exams list to show newly generated exams\n      if (user) {\n        console.log('🔄 Refreshing exams list for new exams...');\n        getExams(true); // Use refresh mode\n      }\n    };\n\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('newExamCreated', handleNewExam);\n    return () => {\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('newExamCreated', handleNewExam);\n    };\n  }, []);\n\n  // Periodic refresh to ensure quiz list stays up to date\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      if (user && !loading && !refreshing) {\n        console.log('🔄 Periodic refresh of quiz list...');\n        getExams(true); // Use refresh mode\n      }\n    }, 5 * 60 * 1000); // Refresh every 5 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [user, loading, refreshing]);\n  useEffect(() => {\n    let filtered = exams;\n    if (searchTerm) {\n      filtered = filtered.filter(exam => {\n        var _exam$name, _exam$subject;\n        return ((_exam$name = exam.name) === null || _exam$name === void 0 ? void 0 : _exam$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n    if (selectedClass) {\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\n    }\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    setFilteredExams(filtered);\n  }, [exams, searchTerm, selectedClass]);\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\n  const handleQuizStart = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n    startTransition(() => {\n      navigate(`/quiz/${quiz._id}/play`);\n    });\n  };\n\n  // Manual refresh function\n  const handleRefresh = async () => {\n    console.log('🔄 Manual refresh triggered...');\n    if (refreshing || loading) return; // Prevent multiple simultaneous refreshes\n\n    try {\n      if (user) {\n        await getExams(true); // Use refresh mode\n        await getUserResults();\n        message.success('Quiz list refreshed successfully!');\n      }\n    } catch (error) {\n      message.error('Failed to refresh quiz list');\n    }\n  };\n  const handleQuizView = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n    // Check if user has attempted this quiz\n    const userResult = userResults[quiz._id];\n    if (!userResult) {\n      message.info('You need to attempt this quiz first to view results.');\n      return;\n    }\n    startTransition(() => {\n      navigate(`/quiz/${quiz._id}/result`);\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading quizzes...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8 sm:mb-12 opacity-0 animate-fade-in\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-8 h-8 sm:w-10 sm:h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",\n          children: \"Challenge Your Brain, Beat the Rest\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\",\n          children: \"Test your knowledge with our comprehensive quizzes designed for You!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-green-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [filteredExams.length, \" Available Quizzes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-blue-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'All Levels']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), lastRefresh && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-xs text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Updated: \", lastRefresh.toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto mb-8 sm:mb-12 opacity-0 animate-fade-in-delay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-4 sm:p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search quizzes by name or subject...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sm:w-48 md:w-64\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(TbFilter, {\n                    className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedClass,\n                  onChange: e => setSelectedClass(e.target.value),\n                  className: \"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), availableClasses.map(className => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: className,\n                    children: [\"Class \", className]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRefresh,\n              disabled: loading || refreshing,\n              className: \"flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95\",\n              title: \"Refresh quiz list\",\n              children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                className: `h-4 w-4 sm:h-5 sm:w-5 ${loading || refreshing ? 'animate-spin' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 hidden sm:inline text-sm sm:text-base\",\n                children: refreshing ? 'Refreshing...' : 'Refresh'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"opacity-0 animate-fade-in-delay-2\",\n        children: filteredExams.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12 sm:py-16\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n              className: \"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-2\",\n              children: \"No Quizzes Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm sm:text-base\",\n              children: searchTerm || selectedClass ? \"Try adjusting your search or filter criteria.\" : \"No quizzes are available for your level at the moment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: filteredExams.map((quiz, index) => /*#__PURE__*/_jsxDEV(QuizCard, {\n            quiz: quiz,\n            userResult: userResults[quiz._id],\n            showResults: true,\n            onStart: handleQuizStart,\n            onView: () => handleQuizView(quiz),\n            index: index\n          }, quiz._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 283,\n    columnNumber: 5\n  }, this);\n};\n\n// Simple QuizCard component without Framer Motion\n_s(Quiz, \"FmJwpU6An2DF6t9gL/MsElfZCNA=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nconst QuizCard = ({\n  quiz,\n  userResult,\n  onStart,\n  onView,\n  index\n}) => {\n  const formatTime = seconds => {\n    if (!seconds) return 'N/A';\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Safety checks for quiz object\n  if (!quiz || typeof quiz !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Invalid quiz data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 transform hover:scale-105 opacity-0 animate-fade-in-stagger\",\n    style: {\n      animationDelay: `${Math.min((index || 0) * 0.1, 0.8)}s`\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-bold text-gray-900 mb-2 line-clamp-2\",\n          children: typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4 text-sm text-gray-600 mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Array.isArray(quiz.questions) ? quiz.questions.length : 0, \" Questions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [typeof quiz.duration === 'number' ? quiz.duration : 0, \" min\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-right\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block px-3 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full\",\n          children: [\"Class \", typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.class : 'N/A']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this), userResult && typeof userResult === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: \"Your Best Score\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-sm font-bold ${userResult.verdict === 'Pass' ? 'text-green-600' : 'text-red-600'}`,\n          children: [typeof userResult.percentage === 'number' ? userResult.percentage : 0, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-xs text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0, \" correct\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Time: \", formatTime(userResult.timeTaken)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onStart(quiz),\n        className: \"flex-1 flex items-center justify-center gap-2 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium text-sm transform hover:scale-105 active:scale-95\",\n        children: [/*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), userResult ? 'Retake' : 'Start']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this), userResult && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onView(quiz),\n        className: \"px-4 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all font-medium text-sm transform hover:scale-105 active:scale-95\",\n        title: \"View Results\",\n        children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 419,\n    columnNumber: 5\n  }, this);\n};\n_c2 = QuizCard;\nexport default Quiz;\nvar _c, _c2;\n$RefreshReg$(_c, \"Quiz\");\n$RefreshReg$(_c2, \"QuizCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useNavigate", "useDispatch", "useSelector", "message", "TbSearch", "Tb<PERSON><PERSON>er", "TbClock", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "TbTarget", "TbCheck", "TbX", "TbStar", "TbHome", "TbBolt", "TbRefresh", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Quiz", "_s", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "refreshing", "setRefreshing", "lastRefresh", "setLastRefresh", "navigate", "dispatch", "user", "state", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "for<PERSON>ach", "report", "_report$exam", "examId", "exam", "result", "Date", "createdAt", "verdict", "percentage", "correctAnswers", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "score", "points", "xpEarned", "xpGained", "timeTaken", "completedAt", "error", "console", "getExams", "isRefresh", "log", "cachedExams", "localStorage", "getItem", "cacheTime", "now", "parseInt", "cached", "JSON", "parse", "class", "String", "userLevelExams", "filter", "level", "toLowerCase", "sortedExams", "sort", "a", "b", "setItem", "stringify", "toString", "handleRankingUpdate", "handleNewExam", "handleWindowFocus", "window", "addEventListener", "removeEventListener", "refreshInterval", "setInterval", "clearInterval", "filtered", "_exam$name", "_exam$subject", "name", "includes", "subject", "availableClasses", "Set", "map", "e", "Boolean", "handleQuizStart", "quiz", "handleRefresh", "handleQuizView", "userResult", "info", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "toLocaleTimeString", "type", "placeholder", "value", "onChange", "target", "onClick", "disabled", "title", "index", "QuizCard", "showResults", "onStart", "onView", "_c", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "padStart", "style", "animationDelay", "min", "Array", "isArray", "questions", "duration", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { message } from 'antd';\r\nimport {\r\n  Tb<PERSON>earch,\r\n  Tb<PERSON><PERSON><PERSON>,\r\n  TbClock,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX,\r\n  TbStar,\r\n  TbHome,\r\n  TbBolt,\r\n  TbRefresh\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport './animations.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedClass, setSelectedClass] = useState('');\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [lastRefresh, setLastRefresh] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const getUserResults = useCallback(async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId || !report.result) return;\r\n\r\n          // Extract data from the result object\r\n          const result = report.result;\r\n\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: result.verdict,\r\n              percentage: result.percentage,\r\n              correctAnswers: result.correctAnswers,\r\n              wrongAnswers: result.wrongAnswers,\r\n              totalQuestions: result.totalQuestions,\r\n              obtainedMarks: result.obtainedMarks,\r\n              totalMarks: result.totalMarks,\r\n              score: result.score,\r\n              points: result.points,\r\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  }, [user?._id]);\r\n\r\n  // Define getExams function outside useEffect so it can be called from other functions\r\n  const getExams = useCallback(async (isRefresh = false) => {\r\n      try {\r\n        // Safety check: ensure user exists before proceeding\r\n        if (!user) {\r\n          console.log(\"User not loaded yet, skipping exam fetch\");\r\n          return;\r\n        }\r\n\r\n        // Check cache first (unless refreshing)\r\n        if (!isRefresh) {\r\n          const cachedExams = localStorage.getItem('user_exams_cache');\r\n          const cacheTime = localStorage.getItem('user_exams_cache_time');\r\n          const now = Date.now();\r\n\r\n          // Use cache if less than 3 minutes old\r\n          if (cachedExams && cacheTime && (now - parseInt(cacheTime)) < 180000) {\r\n            const cached = JSON.parse(cachedExams);\r\n            setExams(cached);\r\n            setLastRefresh(new Date(parseInt(cacheTime)));\r\n            if (user?.class) {\r\n              setSelectedClass(String(user.class));\r\n            }\r\n            setLoading(false);\r\n            return;\r\n          }\r\n        }\r\n\r\n        if (isRefresh) {\r\n          setRefreshing(true);\r\n        } else {\r\n          dispatch(ShowLoading());\r\n        }\r\n\r\n        const response = await getAllExams();\r\n\r\n        if (isRefresh) {\r\n          setRefreshing(false);\r\n        } else {\r\n          dispatch(HideLoading());\r\n        }\r\n\r\n        if (response.success) {\r\n          // Filter exams by user's level with proper null checks\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n          setLastRefresh(new Date());\r\n\r\n          // Cache the exams data\r\n          localStorage.setItem('user_exams_cache', JSON.stringify(sortedExams));\r\n          localStorage.setItem('user_exams_cache_time', Date.now().toString());\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        if (isRefresh) {\r\n          setRefreshing(false);\r\n        } else {\r\n          dispatch(HideLoading());\r\n        }\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n  }, [dispatch, user]);\r\n\r\n  useEffect(() => {\r\n    getExams(false); // Initial load\r\n    getUserResults();\r\n  }, [getExams, getUserResults]);\r\n\r\n  // Real-time updates for quiz completion and new exams\r\n  useEffect(() => {\r\n    // Listen for real-time updates from quiz completion\r\n    const handleRankingUpdate = () => {\r\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\r\n      getUserResults(); // Refresh user results to show updated XP\r\n    };\r\n\r\n    // Listen for new exam creation events\r\n    const handleNewExam = () => {\r\n      console.log('🆕 New exam created - refreshing exam list...');\r\n      if (user) {\r\n        getExams(true); // Use refresh mode\r\n        getUserResults();\r\n      }\r\n    };\r\n\r\n    // Listen for window focus to refresh data when returning from quiz\r\n    const handleWindowFocus = () => {\r\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\r\n      getUserResults();\r\n      // Also refresh exams list to show newly generated exams\r\n      if (user) {\r\n        console.log('🔄 Refreshing exams list for new exams...');\r\n        getExams(true); // Use refresh mode\r\n      }\r\n    };\r\n\r\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\r\n    window.addEventListener('focus', handleWindowFocus);\r\n    window.addEventListener('newExamCreated', handleNewExam);\r\n\r\n    return () => {\r\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\r\n      window.removeEventListener('focus', handleWindowFocus);\r\n      window.removeEventListener('newExamCreated', handleNewExam);\r\n    };\r\n  }, []);\r\n\r\n  // Periodic refresh to ensure quiz list stays up to date\r\n  useEffect(() => {\r\n    const refreshInterval = setInterval(() => {\r\n      if (user && !loading && !refreshing) {\r\n        console.log('🔄 Periodic refresh of quiz list...');\r\n        getExams(true); // Use refresh mode\r\n      }\r\n    }, 5 * 60 * 1000); // Refresh every 5 minutes\r\n\r\n    return () => clearInterval(refreshInterval);\r\n  }, [user, loading, refreshing]);\r\n\r\n  useEffect(() => {\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/play`);\r\n    });\r\n  };\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = async () => {\r\n    console.log('🔄 Manual refresh triggered...');\r\n    if (refreshing || loading) return; // Prevent multiple simultaneous refreshes\r\n\r\n    try {\r\n      if (user) {\r\n        await getExams(true); // Use refresh mode\r\n        await getUserResults();\r\n        message.success('Quiz list refreshed successfully!');\r\n      }\r\n    } catch (error) {\r\n      message.error('Failed to refresh quiz list');\r\n    }\r\n  };\r\n\r\n  const handleQuizView = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    // Check if user has attempted this quiz\r\n    const userResult = userResults[quiz._id];\r\n    if (!userResult) {\r\n      message.info('You need to attempt this quiz first to view results.');\r\n      return;\r\n    }\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/result`);\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading quizzes...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Hero Section */}\r\n        <div className=\"text-center mb-8 sm:mb-12 opacity-0 animate-fade-in\">\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\">\r\n            <TbBrain className=\"w-8 h-8 sm:w-10 sm:h-10 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Challenge Your Brain, Beat the Rest\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\">\r\n            Test your knowledge with our comprehensive quizzes designed for You!\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              <span>{filteredExams.length} Available Quizzes</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>Level: {user?.level || 'All Levels'}</span>\r\n            </div>\r\n            {lastRefresh && (\r\n              <div className=\"flex items-center gap-2 text-xs text-gray-400\">\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full\"></div>\r\n                <span>Updated: {lastRefresh.toLocaleTimeString()}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter */}\r\n        <div className=\"max-w-4xl mx-auto mb-8 sm:mb-12 opacity-0 animate-fade-in-delay\">\r\n          <div className=\"bg-white rounded-2xl shadow-lg p-4 sm:p-6\">\r\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by name or subject...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\r\n                />\r\n              </div>\r\n              <div className=\"sm:w-48 md:w-64\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => setSelectedClass(e.target.value)}\r\n                    className=\"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>Class {className}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Refresh Button */}\r\n              <button\r\n                onClick={handleRefresh}\r\n                disabled={loading || refreshing}\r\n                className=\"flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95\"\r\n                title=\"Refresh quiz list\"\r\n              >\r\n                <TbRefresh className={`h-4 w-4 sm:h-5 sm:w-5 ${(loading || refreshing) ? 'animate-spin' : ''}`} />\r\n                <span className=\"ml-2 hidden sm:inline text-sm sm:text-base\">\r\n                  {refreshing ? 'Refreshing...' : 'Refresh'}\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quiz Grid */}\r\n        <div className=\"opacity-0 animate-fade-in-delay-2\">\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-12 sm:py-16\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 text-sm sm:text-base\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <QuizCard\r\n                  key={quiz._id}\r\n                  quiz={quiz}\r\n                  userResult={userResults[quiz._id]}\r\n                  showResults={true}\r\n                  onStart={handleQuizStart}\r\n                  onView={() => handleQuizView(quiz)}\r\n                  index={index}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Simple QuizCard component without Framer Motion\r\nconst QuizCard = ({ quiz, userResult, onStart, onView, index }) => {\r\n  const formatTime = (seconds) => {\r\n    if (!seconds) return 'N/A';\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  // Safety checks for quiz object\r\n  if (!quiz || typeof quiz !== 'object') {\r\n    return (\r\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\r\n        <p className=\"text-gray-500\">Invalid quiz data</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 transform hover:scale-105 opacity-0 animate-fade-in-stagger\"\r\n      style={{ animationDelay: `${Math.min((index || 0) * 0.1, 0.8)}s` }}\r\n    >\r\n      <div className=\"flex items-start justify-between mb-4\">\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-lg font-bold text-gray-900 mb-2 line-clamp-2\">\r\n            {typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'}\r\n          </h3>\r\n          <div className=\"flex items-center gap-4 text-sm text-gray-600 mb-3\">\r\n            <div className=\"flex items-center gap-1\">\r\n              <TbQuestionMark className=\"w-4 h-4\" />\r\n              <span>{Array.isArray(quiz.questions) ? quiz.questions.length : 0} Questions</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-1\">\r\n              <TbClock className=\"w-4 h-4\" />\r\n              <span>{typeof quiz.duration === 'number' ? quiz.duration : 0} min</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"text-right\">\r\n          <span className=\"inline-block px-3 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full\">\r\n            Class {typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.class : 'N/A'}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {userResult && typeof userResult === 'object' && (\r\n        <div className=\"mb-4 p-3 bg-gray-50 rounded-lg\">\r\n          <div className=\"flex items-center justify-between mb-2\">\r\n            <span className=\"text-sm font-medium text-gray-700\">Your Best Score</span>\r\n            <span className={`text-sm font-bold ${\r\n              userResult.verdict === 'Pass' ? 'text-green-600' : 'text-red-600'\r\n            }`}>\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </span>\r\n          </div>\r\n          <div className=\"flex items-center justify-between text-xs text-gray-600\">\r\n            <span>{typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0} correct</span>\r\n            <span>Time: {formatTime(userResult.timeTaken)}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex gap-2\">\r\n        <button\r\n          onClick={() => onStart(quiz)}\r\n          className=\"flex-1 flex items-center justify-center gap-2 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium text-sm transform hover:scale-105 active:scale-95\"\r\n        >\r\n          <TbPlayerPlay className=\"w-4 h-4\" />\r\n          {userResult ? 'Retake' : 'Start'}\r\n        </button>\r\n\r\n        {userResult && (\r\n          <button\r\n            onClick={() => onView(quiz)}\r\n            className=\"px-4 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all font-medium text-sm transform hover:scale-105 active:scale-95\"\r\n            title=\"View Results\"\r\n          >\r\n            <TbTrophy className=\"w-4 h-4\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,SAAS,QACJ,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM8C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2C;EAAK,CAAC,GAAG1C,WAAW,CAAE2C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,cAAc,GAAGhD,WAAW,CAAC,YAAY;IAC7C,IAAI;MACF,IAAI,EAAC8C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,GAAG,GAAE;MAEhB,MAAMC,QAAQ,GAAG,MAAM7B,mBAAmB,CAAC;QAAE8B,MAAM,EAAEL,IAAI,CAACG;MAAI,CAAC,CAAC;MAEhE,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAACC,MAAM,IAAI;UAAA,IAAAC,YAAA;UAC9B,MAAMC,MAAM,IAAAD,YAAA,GAAGD,MAAM,CAACG,IAAI,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,GAAG;UAC/B,IAAI,CAACS,MAAM,IAAI,CAACF,MAAM,CAACI,MAAM,EAAE;;UAE/B;UACA,MAAMA,MAAM,GAAGJ,MAAM,CAACI,MAAM;UAE5B,IAAI,CAACP,UAAU,CAACK,MAAM,CAAC,IAAI,IAAIG,IAAI,CAACL,MAAM,CAACM,SAAS,CAAC,GAAG,IAAID,IAAI,CAACR,UAAU,CAACK,MAAM,CAAC,CAACI,SAAS,CAAC,EAAE;YAC9FT,UAAU,CAACK,MAAM,CAAC,GAAG;cACnBK,OAAO,EAAEH,MAAM,CAACG,OAAO;cACvBC,UAAU,EAAEJ,MAAM,CAACI,UAAU;cAC7BC,cAAc,EAAEL,MAAM,CAACK,cAAc;cACrCC,YAAY,EAAEN,MAAM,CAACM,YAAY;cACjCC,cAAc,EAAEP,MAAM,CAACO,cAAc;cACrCC,aAAa,EAAER,MAAM,CAACQ,aAAa;cACnCC,UAAU,EAAET,MAAM,CAACS,UAAU;cAC7BC,KAAK,EAAEV,MAAM,CAACU,KAAK;cACnBC,MAAM,EAAEX,MAAM,CAACW,MAAM;cACrBC,QAAQ,EAAEZ,MAAM,CAACY,QAAQ,IAAIZ,MAAM,CAACW,MAAM,IAAIX,MAAM,CAACa,QAAQ,IAAI,CAAC;cAClEC,SAAS,EAAElB,MAAM,CAACkB,SAAS;cAC3BC,WAAW,EAAEnB,MAAM,CAACM;YACtB,CAAC;UACH;QACF,CAAC,CAAC;QACFzB,cAAc,CAACgB,UAAU,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAAC9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,GAAG,CAAC,CAAC;;EAEf;EACA,MAAM6B,QAAQ,GAAG9E,WAAW,CAAC,OAAO+E,SAAS,GAAG,KAAK,KAAK;IACtD,IAAI;MACF;MACA,IAAI,CAACjC,IAAI,EAAE;QACT+B,OAAO,CAACG,GAAG,CAAC,0CAA0C,CAAC;QACvD;MACF;;MAEA;MACA,IAAI,CAACD,SAAS,EAAE;QACd,MAAME,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;QAC5D,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC/D,MAAME,GAAG,GAAGxB,IAAI,CAACwB,GAAG,CAAC,CAAC;;QAEtB;QACA,IAAIJ,WAAW,IAAIG,SAAS,IAAKC,GAAG,GAAGC,QAAQ,CAACF,SAAS,CAAC,GAAI,MAAM,EAAE;UACpE,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACR,WAAW,CAAC;UACtCpD,QAAQ,CAAC0D,MAAM,CAAC;UAChB5C,cAAc,CAAC,IAAIkB,IAAI,CAACyB,QAAQ,CAACF,SAAS,CAAC,CAAC,CAAC;UAC7C,IAAItC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4C,KAAK,EAAE;YACfvD,gBAAgB,CAACwD,MAAM,CAAC7C,IAAI,CAAC4C,KAAK,CAAC,CAAC;UACtC;UACAnD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;MAEA,IAAIwC,SAAS,EAAE;QACbtC,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLI,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MACzB;MAEA,MAAM2B,QAAQ,GAAG,MAAM9B,WAAW,CAAC,CAAC;MAEpC,IAAI2D,SAAS,EAAE;QACbtC,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,MAAM;QACLI,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACzB;MAEA,IAAI4B,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMwC,cAAc,GAAG1C,QAAQ,CAACI,IAAI,CAACuC,MAAM,CAAClC,IAAI,IAAI;UAClD,IAAI,CAACA,IAAI,CAACmC,KAAK,IAAI,CAAChD,IAAI,IAAI,CAACA,IAAI,CAACgD,KAAK,EAAE,OAAO,KAAK;UACrD,OAAOnC,IAAI,CAACmC,KAAK,CAACC,WAAW,CAAC,CAAC,KAAKjD,IAAI,CAACgD,KAAK,CAACC,WAAW,CAAC,CAAC;QAC9D,CAAC,CAAC;QAEF,MAAMC,WAAW,GAAGJ,cAAc,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAItC,IAAI,CAACsC,CAAC,CAACrC,SAAS,CAAC,GAAG,IAAID,IAAI,CAACqC,CAAC,CAACpC,SAAS,CAAC,CAAC;QAChGjC,QAAQ,CAACmE,WAAW,CAAC;QACrBrD,cAAc,CAAC,IAAIkB,IAAI,CAAC,CAAC,CAAC;;QAE1B;QACAqB,YAAY,CAACkB,OAAO,CAAC,kBAAkB,EAAEZ,IAAI,CAACa,SAAS,CAACL,WAAW,CAAC,CAAC;QACrEd,YAAY,CAACkB,OAAO,CAAC,uBAAuB,EAAEvC,IAAI,CAACwB,GAAG,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC,CAAC;;QAEpE;QACA,IAAIxD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4C,KAAK,EAAE;UACfvD,gBAAgB,CAACwD,MAAM,CAAC7C,IAAI,CAAC4C,KAAK,CAAC,CAAC;QACtC;MACF,CAAC,MAAM;QACLrF,OAAO,CAACuE,KAAK,CAAC1B,QAAQ,CAAC7C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuE,KAAK,EAAE;MACd,IAAIG,SAAS,EAAE;QACbtC,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,MAAM;QACLI,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACzB;MACAjB,OAAO,CAACuE,KAAK,CAACA,KAAK,CAACvE,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRkC,UAAU,CAAC,KAAK,CAAC;IACnB;EACJ,CAAC,EAAE,CAACM,QAAQ,EAAEC,IAAI,CAAC,CAAC;EAEpB/C,SAAS,CAAC,MAAM;IACd+E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IACjB9B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC8B,QAAQ,EAAE9B,cAAc,CAAC,CAAC;;EAE9B;EACAjD,SAAS,CAAC,MAAM;IACd;IACA,MAAMwG,mBAAmB,GAAGA,CAAA,KAAM;MAChC1B,OAAO,CAACG,GAAG,CAAC,4DAA4D,CAAC;MACzEhC,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMwD,aAAa,GAAGA,CAAA,KAAM;MAC1B3B,OAAO,CAACG,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAIlC,IAAI,EAAE;QACRgC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAChB9B,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;;IAED;IACA,MAAMyD,iBAAiB,GAAGA,CAAA,KAAM;MAC9B5B,OAAO,CAACG,GAAG,CAAC,sDAAsD,CAAC;MACnEhC,cAAc,CAAC,CAAC;MAChB;MACA,IAAIF,IAAI,EAAE;QACR+B,OAAO,CAACG,GAAG,CAAC,2CAA2C,CAAC;QACxDF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB;IACF,CAAC;;IAED4B,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAC7DG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;IACnDC,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,EAAEH,aAAa,CAAC;IAExD,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;MAChEG,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;MACtDC,MAAM,CAACE,mBAAmB,CAAC,gBAAgB,EAAEJ,aAAa,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzG,SAAS,CAAC,MAAM;IACd,MAAM8G,eAAe,GAAGC,WAAW,CAAC,MAAM;MACxC,IAAIhE,IAAI,IAAI,CAACR,OAAO,IAAI,CAACE,UAAU,EAAE;QACnCqC,OAAO,CAACG,GAAG,CAAC,qCAAqC,CAAC;QAClDF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;IAEnB,OAAO,MAAMiC,aAAa,CAACF,eAAe,CAAC;EAC7C,CAAC,EAAE,CAAC/D,IAAI,EAAER,OAAO,EAAEE,UAAU,CAAC,CAAC;EAE/BzC,SAAS,CAAC,MAAM;IACd,IAAIiH,QAAQ,GAAGpF,KAAK;IACpB,IAAII,UAAU,EAAE;MACdgF,QAAQ,GAAGA,QAAQ,CAACnB,MAAM,CAAClC,IAAI;QAAA,IAAAsD,UAAA,EAAAC,aAAA;QAAA,OAC7B,EAAAD,UAAA,GAAAtD,IAAI,CAACwD,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWlB,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAACpF,UAAU,CAAC+D,WAAW,CAAC,CAAC,CAAC,OAAAmB,aAAA,GAC3DvD,IAAI,CAAC0D,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcnB,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAACpF,UAAU,CAAC+D,WAAW,CAAC,CAAC,CAAC;MAAA,CAChE,CAAC;IACH;IACA,IAAI7D,aAAa,EAAE;MACjB8E,QAAQ,GAAGA,QAAQ,CAACnB,MAAM,CAAClC,IAAI,IAAIgC,MAAM,CAAChC,IAAI,CAAC+B,KAAK,CAAC,KAAKC,MAAM,CAACzD,aAAa,CAAC,CAAC;IAClF;IACA8E,QAAQ,CAACf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAItC,IAAI,CAACsC,CAAC,CAACrC,SAAS,CAAC,GAAG,IAAID,IAAI,CAACqC,CAAC,CAACpC,SAAS,CAAC,CAAC;IACtE/B,gBAAgB,CAACiF,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACpF,KAAK,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAMoF,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC3F,KAAK,CAAC4F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC/B,KAAK,CAAC,CAACG,MAAM,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACzB,IAAI,CAAC,CAAC;EAErF,MAAM0B,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC3E,GAAG,EAAE;MACtB5C,OAAO,CAACuE,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;IACA3E,eAAe,CAAC,MAAM;MACpB2C,QAAQ,CAAE,SAAQgF,IAAI,CAAC3E,GAAI,OAAM,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChChD,OAAO,CAACG,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAIxC,UAAU,IAAIF,OAAO,EAAE,OAAO,CAAC;;IAEnC,IAAI;MACF,IAAIQ,IAAI,EAAE;QACR,MAAMgC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACtB,MAAM9B,cAAc,CAAC,CAAC;QACtB3C,OAAO,CAAC+C,OAAO,CAAC,mCAAmC,CAAC;MACtD;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdvE,OAAO,CAACuE,KAAK,CAAC,6BAA6B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMkD,cAAc,GAAIF,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC3E,GAAG,EAAE;MACtB5C,OAAO,CAACuE,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;IACA;IACA,MAAMmD,UAAU,GAAG3F,WAAW,CAACwF,IAAI,CAAC3E,GAAG,CAAC;IACxC,IAAI,CAAC8E,UAAU,EAAE;MACf1H,OAAO,CAAC2H,IAAI,CAAC,sDAAsD,CAAC;MACpE;IACF;IACA/H,eAAe,CAAC,MAAM;MACpB2C,QAAQ,CAAE,SAAQgF,IAAI,CAAC3E,GAAI,SAAQ,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC;EAID,IAAIX,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKwG,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGzG,OAAA;QAAKwG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzG,OAAA;UAAKwG,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG7G,OAAA;UAAGwG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7G,OAAA;IAAKwG,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxEzG,OAAA;MAAKwG,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAE1EzG,OAAA;QAAKwG,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClEzG,OAAA;UAAKwG,SAAS,EAAC,qJAAqJ;UAAAC,QAAA,eAClKzG,OAAA,CAACb,OAAO;YAACqH,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACN7G,OAAA;UAAIwG,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EAAC;QAEvG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7G,OAAA;UAAGwG,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAAC;QAEjG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7G,OAAA;UAAKwG,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBAC9GzG,OAAA;YAAKwG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzG,OAAA;cAAKwG,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzD7G,OAAA;cAAAyG,QAAA,GAAOpG,aAAa,CAACyG,MAAM,EAAC,oBAAkB;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN7G,OAAA;YAAKwG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzG,OAAA;cAAKwG,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD7G,OAAA;cAAAyG,QAAA,GAAM,SAAO,EAAC,CAAApF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgD,KAAK,KAAI,YAAY;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EACL5F,WAAW,iBACVjB,OAAA;YAAKwG,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DzG,OAAA;cAAKwG,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD7G,OAAA;cAAAyG,QAAA,GAAM,WAAS,EAACxF,WAAW,CAAC8F,kBAAkB,CAAC,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7G,OAAA;QAAKwG,SAAS,EAAC,iEAAiE;QAAAC,QAAA,eAC9EzG,OAAA;UAAKwG,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxDzG,OAAA;YAAKwG,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDzG,OAAA;cAAKwG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzG,OAAA;gBAAKwG,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,eAC3FzG,OAAA,CAACnB,QAAQ;kBAAC2H,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACN7G,OAAA;gBACEgH,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,sCAAsC;gBAClDC,KAAK,EAAE3G,UAAW;gBAClB4G,QAAQ,EAAGnB,CAAC,IAAKxF,aAAa,CAACwF,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE;gBAC/CV,SAAS,EAAC;cAAmO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9O,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7G,OAAA;cAAKwG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzG,OAAA;gBAAKwG,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBzG,OAAA;kBAAKwG,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,eAC3FzG,OAAA,CAAClB,QAAQ;oBAAC0H,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACN7G,OAAA;kBACEkH,KAAK,EAAEzG,aAAc;kBACrB0G,QAAQ,EAAGnB,CAAC,IAAKtF,gBAAgB,CAACsF,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE;kBAClDV,SAAS,EAAC,2OAA2O;kBAAAC,QAAA,gBAErPzG,OAAA;oBAAQkH,KAAK,EAAC,EAAE;oBAAAT,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpChB,gBAAgB,CAACE,GAAG,CAAES,SAAS,iBAC9BxG,OAAA;oBAAwBkH,KAAK,EAAEV,SAAU;oBAAAC,QAAA,GAAC,QAAM,EAACD,SAAS;kBAAA,GAA7CA,SAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6C,CACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7G,OAAA;cACEqH,OAAO,EAAEjB,aAAc;cACvBkB,QAAQ,EAAEzG,OAAO,IAAIE,UAAW;cAChCyF,SAAS,EAAC,4VAA4V;cACtWe,KAAK,EAAC,mBAAmB;cAAAd,QAAA,gBAEzBzG,OAAA,CAACN,SAAS;gBAAC8G,SAAS,EAAG,yBAAyB3F,OAAO,IAAIE,UAAU,GAAI,cAAc,GAAG,EAAG;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClG7G,OAAA;gBAAMwG,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACzD1F,UAAU,GAAG,eAAe,GAAG;cAAS;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7G,OAAA;QAAKwG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC/CpG,aAAa,CAACyG,MAAM,KAAK,CAAC,gBACzB9G,OAAA;UAAKwG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzCzG,OAAA;YAAKwG,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1EzG,OAAA,CAACZ,QAAQ;cAACoH,SAAS,EAAC;YAAsD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7E7G,OAAA;cAAIwG,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzF7G,OAAA;cAAGwG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAC9ClG,UAAU,IAAIE,aAAa,GACxB,+CAA+C,GAC/C;YAAwD;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN7G,OAAA;UAAKwG,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEpG,aAAa,CAAC0F,GAAG,CAAC,CAACI,IAAI,EAAEqB,KAAK,kBAC7BxH,OAAA,CAACyH,QAAQ;YAEPtB,IAAI,EAAEA,IAAK;YACXG,UAAU,EAAE3F,WAAW,CAACwF,IAAI,CAAC3E,GAAG,CAAE;YAClCkG,WAAW,EAAE,IAAK;YAClBC,OAAO,EAAEzB,eAAgB;YACzB0B,MAAM,EAAEA,CAAA,KAAMvB,cAAc,CAACF,IAAI,CAAE;YACnCqB,KAAK,EAAEA;UAAM,GANRrB,IAAI,CAAC3E,GAAG;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOd,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA3G,EAAA,CAtXMD,IAAI;EAAA,QASSxB,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAkJ,EAAA,GAXxB5H,IAAI;AAuXV,MAAMwH,QAAQ,GAAGA,CAAC;EAAEtB,IAAI;EAAEG,UAAU;EAAEqB,OAAO;EAAEC,MAAM;EAAEJ;AAAM,CAAC,KAAK;EACjE,MAAMM,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACtD,QAAQ,CAAC,CAAC,CAACuD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,IAAI,CAACjC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,oBACEnG,OAAA;MAAKwG,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eACxEzG,OAAA;QAAGwG,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,oBACE7G,OAAA;IACEwG,SAAS,EAAC,mKAAmK;IAC7K6B,KAAK,EAAE;MAAEC,cAAc,EAAG,GAAEL,IAAI,CAACM,GAAG,CAAC,CAACf,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,CAAE;IAAG,CAAE;IAAAf,QAAA,gBAEnEzG,OAAA;MAAKwG,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzG,OAAA;QAAKwG,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBzG,OAAA;UAAIwG,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC9D,OAAON,IAAI,CAACT,IAAI,KAAK,QAAQ,GAAGS,IAAI,CAACT,IAAI,GAAG;QAAe;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACL7G,OAAA;UAAKwG,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBACjEzG,OAAA;YAAKwG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzG,OAAA,CAAChB,cAAc;cAACwH,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtC7G,OAAA;cAAAyG,QAAA,GAAO+B,KAAK,CAACC,OAAO,CAACtC,IAAI,CAACuC,SAAS,CAAC,GAAGvC,IAAI,CAACuC,SAAS,CAAC5B,MAAM,GAAG,CAAC,EAAC,YAAU;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACN7G,OAAA;YAAKwG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzG,OAAA,CAACjB,OAAO;cAACyH,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B7G,OAAA;cAAAyG,QAAA,GAAO,OAAON,IAAI,CAACwC,QAAQ,KAAK,QAAQ,GAAGxC,IAAI,CAACwC,QAAQ,GAAG,CAAC,EAAC,MAAI;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7G,OAAA;QAAKwG,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBzG,OAAA;UAAMwG,SAAS,EAAC,qFAAqF;UAAAC,QAAA,GAAC,QAC9F,EAAC,OAAON,IAAI,CAAClC,KAAK,KAAK,QAAQ,IAAI,OAAOkC,IAAI,CAAClC,KAAK,KAAK,QAAQ,GAAGkC,IAAI,CAAClC,KAAK,GAAG,KAAK;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELP,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,iBAC3CtG,OAAA;MAAKwG,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CzG,OAAA;QAAKwG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzG,OAAA;UAAMwG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1E7G,OAAA;UAAMwG,SAAS,EAAG,qBAChBF,UAAU,CAAChE,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,cACpD,EAAE;UAAAmE,QAAA,GACA,OAAOH,UAAU,CAAC/D,UAAU,KAAK,QAAQ,GAAG+D,UAAU,CAAC/D,UAAU,GAAG,CAAC,EAAC,GACzE;QAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7G,OAAA;QAAKwG,SAAS,EAAC,yDAAyD;QAAAC,QAAA,gBACtEzG,OAAA;UAAAyG,QAAA,GAAO,OAAOH,UAAU,CAAC9D,cAAc,KAAK,QAAQ,GAAG8D,UAAU,CAAC9D,cAAc,GAAG,CAAC,EAAC,UAAQ;QAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpG7G,OAAA;UAAAyG,QAAA,GAAM,QAAM,EAACqB,UAAU,CAACxB,UAAU,CAACrD,SAAS,CAAC;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED7G,OAAA;MAAKwG,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBzG,OAAA;QACEqH,OAAO,EAAEA,CAAA,KAAMM,OAAO,CAACxB,IAAI,CAAE;QAC7BK,SAAS,EAAC,2TAA2T;QAAAC,QAAA,gBAErUzG,OAAA,CAACd,YAAY;UAACsH,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnCP,UAAU,GAAG,QAAQ,GAAG,OAAO;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EAERP,UAAU,iBACTtG,OAAA;QACEqH,OAAO,EAAEA,CAAA,KAAMO,MAAM,CAACzB,IAAI,CAAE;QAC5BK,SAAS,EAAC,yNAAyN;QACnOe,KAAK,EAAC,cAAc;QAAAd,QAAA,eAEpBzG,OAAA,CAACf,QAAQ;UAACuH,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC+B,GAAA,GAnFInB,QAAQ;AAqFd,eAAexH,IAAI;AAAC,IAAA4H,EAAA,EAAAe,GAAA;AAAAC,YAAA,CAAAhB,EAAA;AAAAgB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbFlag } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\nimport './quiz-improvements.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Initialize user from localStorage if not in Redux\n  useEffect(() => {\n    if (!user) {\n      const storedUser = localStorage.getItem('user');\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('No token found, redirecting to login');\n        message.error('Please login to access quizzes');\n        navigate('/login');\n        return;\n      }\n      if (storedUser) {\n        try {\n          const userData = JSON.parse(storedUser);\n          console.log('QuizPlay: User found in localStorage:', userData.name);\n          // The ProtectedRoute will handle setting the user in Redux\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          navigate('/login');\n        }\n      }\n    }\n  }, [user, navigate]);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz data for ID:', id);\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            navigate('/quiz');\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            navigate('/quiz');\n            return;\n          }\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          navigate('/quiz');\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        navigate('/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz - wrapped in useCallback to fix dependency warning\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      // Get user data from Redux or localStorage\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        navigate('/login');\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          timeTaken\n        }\n      };\n      console.log('Submitting quiz result:', reportData);\n      await addReport(reportData);\n\n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n  if (loading || !user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: !user ? 'Checking authentication...' : 'Loading quiz...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this);\n  }\n  if (!examData || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbX, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"Quiz Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz could not be loaded or has no questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Early return if user is null to prevent errors\n  if (user === null) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Checking authentication...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = (currentQuestionIndex + 1) / questions.length * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900 truncate\",\n              children: examData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progress}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round(progress), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`,\n                children: currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'Multiple Choice' : 'Fill in the Blank'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), answers[currentQuestionIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1 text-white text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), \"Answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 sm:p-6 flex-1 overflow-y-auto\",\n            style: {\n              maxHeight: 'calc(100vh - 280px)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                children: currentQuestion.name || currentQuestion.question\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), currentQuestion.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentQuestion.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"w-full max-w-full mx-auto h-auto object-contain rounded-lg\",\n                    style: {\n                      maxHeight: '300px'\n                    },\n                    onError: e => {\n                      console.error('Image failed to load:', currentQuestion.imageUrl);\n                      e.target.style.display = 'none';\n                      const errorDiv = e.target.nextElementSibling;\n                      if (errorDiv) {\n                        errorDiv.style.display = 'flex';\n                      }\n                    },\n                    onLoad: () => {\n                      console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                        className: \"w-12 h-12 mx-auto mb-2 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 375,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: \"Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 376,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: \"Please check your internet connection\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: (currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ?\n              // Multiple Choice Questions - Enhanced for mobile\n              Object.entries(currentQuestion.options).map(([key, value]) => {\n                const isSelected = answers[currentQuestionIndex] === key;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(key),\n                  className: `w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                  whileHover: {\n                    scale: 1.01\n                  },\n                  whileTap: {\n                    scale: 0.99\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start gap-3 sm:gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 409,\n                        columnNumber: 43\n                      }, this) : key\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 25\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 23\n                }, this);\n              }) :\n              /*#__PURE__*/\n              // Enhanced Fill-in-the-Blank Questions with better spacing\n              _jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-sm font-semibold text-gray-700\",\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: answers[currentQuestionIndex] || '',\n                  onChange: e => handleAnswerSelect(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\",\n                  rows: \"4\",\n                  style: {\n                    minHeight: '100px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-xs text-gray-500\",\n                  children: \"Tip: Take your time to write a clear and complete answer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, currentQuestionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestionIndex === 0,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${currentQuestionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\",\n            children: questions.map((_, index) => {\n              const isAnswered = answers[index] !== undefined && answers[index] !== '';\n              const isCurrent = index === currentQuestionIndex;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentQuestionIndex(index),\n                className: `w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${isCurrent ? 'bg-blue-600 text-white scale-110' : isAnswered ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'}`,\n                children: isAnswered && !isCurrent ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 23\n                }, this) : index + 1\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext,\n            className: \"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"Submit Quiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sm:hidden\",\n                  children: \"Submit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this), currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(TbFlag, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"lE6uH25rXOZyNMMJpVBaoagyQbY=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useSelector", "message", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbFlag", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizPlay", "_s", "id", "navigate", "user", "state", "storedUser", "localStorage", "getItem", "token", "console", "log", "error", "userData", "JSON", "parse", "name", "examData", "setExamData", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loading", "setLoading", "loadQuizData", "response", "examId", "success", "data", "length", "duration", "Date", "handleSubmitQuiz", "currentUser", "_id", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "percentage", "round", "reportData", "exam", "result", "wrongAnswers", "totalQuestions", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "answer", "goToNext", "goToPrevious", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "progress", "isTimeWarning", "div", "initial", "width", "animate", "transition", "mode", "opacity", "x", "exit", "type", "answerType", "style", "maxHeight", "imageUrl", "src", "alt", "onError", "e", "target", "display", "errorDiv", "nextElement<PERSON><PERSON>ling", "onLoad", "options", "Object", "entries", "key", "value", "isSelected", "button", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "minHeight", "disabled", "_", "isAnswered", "undefined", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Tb<PERSON>lock, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck, \n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbFlag\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\nimport './quiz-improvements.css';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n\n  // Initialize user from localStorage if not in Redux\n  useEffect(() => {\n    if (!user) {\n      const storedUser = localStorage.getItem('user');\n      const token = localStorage.getItem('token');\n\n      if (!token) {\n        console.log('No token found, redirecting to login');\n        message.error('Please login to access quizzes');\n        navigate('/login');\n        return;\n      }\n\n      if (storedUser) {\n        try {\n          const userData = JSON.parse(storedUser);\n          console.log('QuizPlay: User found in localStorage:', userData.name);\n          // The ProtectedRoute will handle setting the user in Redux\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          navigate('/login');\n        }\n      }\n    }\n  }, [user, navigate]);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz data for ID:', id);\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            navigate('/quiz');\n            return;\n          }\n\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            navigate('/quiz');\n            return;\n          }\n\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          navigate('/quiz');\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        navigate('/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz - wrapped in useCallback to fix dependency warning\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      // Get user data from Redux or localStorage\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        navigate('/login');\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          timeTaken\n        }\n      };\n\n      console.log('Submitting quiz result:', reportData);\n      await addReport(reportData);\n\n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  if (loading || !user) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">\n            {!user ? 'Checking authentication...' : 'Loading quiz...'}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbX className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-bold text-gray-900 mb-2\">Quiz Not Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz could not be loaded or has no questions.</p>\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Early return if user is null to prevent errors\n  if (user === null) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Checking authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\">\n      {/* Enhanced Header with better mobile support */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Info */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-xl font-bold text-gray-900 truncate\">\n                {examData.name}\n              </h1>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Question {currentQuestionIndex + 1} of {questions.length}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${\n              isTimeWarning \n                ? 'bg-red-100 text-red-700 animate-pulse' \n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-5 h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(progress)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Main Content with better mobile support */}\n      <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\"\n          >\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                  currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options'\n                    ? 'bg-blue-100 text-blue-800'\n                    : 'bg-green-100 text-green-800'\n                }`}>\n                  {currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' \n                    ? 'Multiple Choice' \n                    : 'Fill in the Blank'}\n                </div>\n                {answers[currentQuestionIndex] && (\n                  <div className=\"flex items-center gap-1 text-white text-sm font-medium\">\n                    <TbCheck className=\"w-4 h-4\" />\n                    Answered\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Question Content - Fixed scrolling and responsive design */}\n            <div className=\"p-4 sm:p-6 flex-1 overflow-y-auto\" style={{ maxHeight: 'calc(100vh - 280px)' }}>\n              <div className=\"mb-6\">\n                <h2 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                  {currentQuestion.name || currentQuestion.question}\n                </h2>\n\n                {/* Enhanced Image Display with better error handling */}\n                {currentQuestion.imageUrl && (\n                  <div className=\"mb-6\">\n                    <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\">\n                      <img\n                        src={currentQuestion.imageUrl}\n                        alt=\"Question diagram\"\n                        className=\"w-full max-w-full mx-auto h-auto object-contain rounded-lg\"\n                        style={{ maxHeight: '300px' }}\n                        onError={(e) => {\n                          console.error('Image failed to load:', currentQuestion.imageUrl);\n                          e.target.style.display = 'none';\n                          const errorDiv = e.target.nextElementSibling;\n                          if (errorDiv) {\n                            errorDiv.style.display = 'flex';\n                          }\n                        }}\n                        onLoad={() => {\n                          console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                        }}\n                      />\n                      <div className=\"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\">\n                        <div className=\"text-center\">\n                          <TbPhoto className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                          <p className=\"text-sm\">Image could not be loaded</p>\n                          <p className=\"text-xs text-gray-400 mt-1\">Please check your internet connection</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Enhanced Answer Options with better responsive design */}\n              <div className=\"space-y-3 sm:space-y-4\">\n                {(currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ? (\n                  // Multiple Choice Questions - Enhanced for mobile\n                  Object.entries(currentQuestion.options).map(([key, value]) => {\n                    const isSelected = answers[currentQuestionIndex] === key;\n                    return (\n                      <motion.button\n                        key={key}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 shadow-md'\n                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                        }`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <div className=\"flex items-start gap-3 sm:gap-4\">\n                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" /> : key}\n                          </div>\n                          <span className=\"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\">{value}</span>\n                        </div>\n                      </motion.button>\n                    );\n                  })\n                ) : (\n                  // Enhanced Fill-in-the-Blank Questions with better spacing\n                  <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\">\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                        <TbEdit className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                      </div>\n                      <label className=\"text-sm font-semibold text-gray-700\">\n                        Your Answer:\n                      </label>\n                    </div>\n                    <textarea\n                      value={answers[currentQuestionIndex] || ''}\n                      onChange={(e) => handleAnswerSelect(e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\"\n                      rows=\"4\"\n                      style={{ minHeight: '100px' }}\n                    />\n                    <div className=\"mt-2 text-xs text-gray-500\">\n                      Tip: Take your time to write a clear and complete answer\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Enhanced Navigation Footer - Mobile Responsive */}\n      <div className=\"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Previous Button - Mobile Optimized */}\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${\n                currentQuestionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n              <span className=\"sm:hidden\">Prev</span>\n            </button>\n\n            {/* Question Navigation Dots - Enhanced for mobile */}\n            <div className=\"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\">\n              {questions.map((_, index) => {\n                const isAnswered = answers[index] !== undefined && answers[index] !== '';\n                const isCurrent = index === currentQuestionIndex;\n                return (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentQuestionIndex(index)}\n                    className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${\n                      isCurrent\n                        ? 'bg-blue-600 text-white scale-110'\n                        : isAnswered\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-200 text-gray-600 hover:bg-gray-300'\n                    }`}\n                  >\n                    {isAnswered && !isCurrent ? (\n                      <TbCheck className=\"w-4 h-4\" />\n                    ) : (\n                      index + 1\n                    )}\n                  </button>\n                );\n              })}\n            </div>\n\n            {/* Next/Submit Button - Mobile Optimized */}\n            <button\n              onClick={currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext}\n              className=\"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\"\n            >\n              <span>\n                {currentQuestionIndex === questions.length - 1 ? (\n                  <>\n                    <span className=\"hidden sm:inline\">Submit Quiz</span>\n                    <span className=\"sm:hidden\">Submit</span>\n                  </>\n                ) : (\n                  'Next'\n                )}\n              </span>\n              {currentQuestionIndex === questions.length - 1 ? (\n                <TbFlag className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              ) : (\n                <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAO,kBAAkB;AACzB,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGtB,SAAS,CAAC,CAAC;EAC1B,MAAMuB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA1B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0B,IAAI,EAAE;MACT,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,MAAMC,KAAK,GAAGF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAI,CAACC,KAAK,EAAE;QACVC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD5B,OAAO,CAAC6B,KAAK,CAAC,gCAAgC,CAAC;QAC/CT,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEA,IAAIG,UAAU,EAAE;QACd,IAAI;UACF,MAAMO,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACT,UAAU,CAAC;UACvCI,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEE,QAAQ,CAACG,IAAI,CAAC;UACnE;QACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvDT,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF;IACF;EACF,CAAC,EAAE,CAACC,IAAI,EAAED,QAAQ,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChBpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAET,EAAE,CAAC;QAE5C,MAAM8B,QAAQ,GAAG,MAAMtC,WAAW,CAAC;UAAEuC,MAAM,EAAE/B;QAAG,CAAC,CAAC;QAClDQ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqB,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClBpD,OAAO,CAAC6B,KAAK,CAAC,qBAAqB,CAAC;YACpCT,QAAQ,CAAC,OAAO,CAAC;YACjB;UACF;UAEA,IAAI,CAAC6B,QAAQ,CAACG,IAAI,CAAChB,SAAS,IAAIa,QAAQ,CAACG,IAAI,CAAChB,SAAS,CAACiB,MAAM,KAAK,CAAC,EAAE;YACpErD,OAAO,CAAC6B,KAAK,CAAC,sCAAsC,CAAC;YACrDT,QAAQ,CAAC,OAAO,CAAC;YACjB;UACF;UAEAe,WAAW,CAACc,QAAQ,CAACG,IAAI,CAAC;UAC1Bf,YAAY,CAACY,QAAQ,CAACG,IAAI,CAAChB,SAAS,IAAI,EAAE,CAAC;UAC3CO,WAAW,CAAC,CAACM,QAAQ,CAACG,IAAI,CAACE,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC;UAChDT,YAAY,CAAC,IAAIU,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC,MAAM;UACL5B,OAAO,CAACE,KAAK,CAAC,iBAAiB,EAAEoB,QAAQ,CAACjD,OAAO,CAAC;UAClDA,OAAO,CAAC6B,KAAK,CAACoB,QAAQ,CAACjD,OAAO,IAAI,qBAAqB,CAAC;UACxDoB,QAAQ,CAAC,OAAO,CAAC;QACnB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C7B,OAAO,CAAC6B,KAAK,CAAC,wCAAwC,CAAC;QACvDT,QAAQ,CAAC,OAAO,CAAC;MACnB,CAAC,SAAS;QACR2B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAI5B,EAAE,IAAIE,IAAI,EAAE;MACd2B,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC7B,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMmC,gBAAgB,GAAG5D,WAAW,CAAC,YAAY;IAC/C,IAAI;MACF;MACA,IAAI6D,WAAW,GAAGpC,IAAI;MACtB,IAAI,CAACoC,WAAW,IAAI,CAACA,WAAW,CAACC,GAAG,EAAE;QACpC,MAAMnC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIF,UAAU,EAAE;UACd,IAAI;YACFkC,WAAW,GAAG1B,IAAI,CAACC,KAAK,CAACT,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOM,KAAK,EAAE;YACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACzD;QACF;MACF;MAEA,IAAI,CAAC4B,WAAW,IAAI,CAACA,WAAW,CAACC,GAAG,EAAE;QACpC1D,OAAO,CAAC6B,KAAK,CAAC,2CAA2C,CAAC;QAC1DT,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEA,MAAMuC,OAAO,GAAG,IAAIJ,IAAI,CAAC,CAAC;MAC1B,MAAMK,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGf,SAAS,IAAI,IAAI,CAAC;;MAE1D;MACA,IAAImB,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAG5B,SAAS,CAAC6B,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAG5B,OAAO,CAAC2B,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLG,QAAQ,EAAEA,QAAQ,CAACR,GAAG;UACtBU,UAAU;UACVE,aAAa,EAAEJ,QAAQ,CAACI,aAAa;UACrCD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAME,UAAU,GAAGV,IAAI,CAACW,KAAK,CAAET,cAAc,GAAG3B,SAAS,CAACiB,MAAM,GAAI,GAAG,CAAC;;MAExE;MACA,MAAMoB,UAAU,GAAG;QACjBC,IAAI,EAAEvD,EAAE;QACRE,IAAI,EAAEoC,WAAW,CAACC,GAAG;QACrBiB,MAAM,EAAE;UACNZ,cAAc;UACda,YAAY,EAAExC,SAAS,CAACiB,MAAM,GAAGU,cAAc;UAC/CQ,UAAU;UACVX;QACF;MACF,CAAC;MAEDjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6C,UAAU,CAAC;MAClD,MAAM7D,SAAS,CAAC6D,UAAU,CAAC;;MAE3B;MACArD,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;QAC7BG,KAAK,EAAE;UACLiD,UAAU;UACVR,cAAc;UACdc,cAAc,EAAEzC,SAAS,CAACiB,MAAM;UAChCO,SAAS;UACTI;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC,EAAE,CAACe,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAErB,EAAE,EAAEC,QAAQ,CAAC,CAAC;;EAEjD;EACAzB,SAAS,CAAC,MAAM;IACd,IAAI+C,QAAQ,IAAI,CAAC,EAAE;MACjBc,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMsB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BpC,WAAW,CAACqC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACpC,QAAQ,EAAEc,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAM0B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGvB,IAAI,CAACC,KAAK,CAACqB,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrChD,UAAU,CAACuC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAAC1C,oBAAoB,GAAGmD;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIpD,oBAAoB,GAAGF,SAAS,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC/Cd,uBAAuB,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIrD,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACyC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,IAAIlC,OAAO,IAAI,CAACzB,IAAI,EAAE;IACpB,oBACEP,OAAA;MAAK8E,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/E,OAAA;UAAK8E,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGnF,OAAA;UAAG8E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACrC,CAACxE,IAAI,GAAG,4BAA4B,GAAG;QAAiB;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC/D,QAAQ,IAAIE,SAAS,CAACiB,MAAM,KAAK,CAAC,EAAE;IACvC,oBACEvC,OAAA;MAAK8E,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/E,OAAA,CAACP,GAAG;UAACqF,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDnF,OAAA;UAAI8E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EnF,OAAA;UAAG8E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxFnF,OAAA;UACEoF,OAAO,EAAEA,CAAA,KAAM9E,QAAQ,CAAC,YAAY,CAAE;UACtCwE,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI5E,IAAI,KAAK,IAAI,EAAE;IACjB,oBACEP,OAAA;MAAK8E,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/E,OAAA;UAAK8E,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGnF,OAAA;UAAG8E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAG/D,SAAS,CAACE,oBAAoB,CAAC;EACvD,MAAM8D,QAAQ,GAAI,CAAC9D,oBAAoB,GAAG,CAAC,IAAIF,SAAS,CAACiB,MAAM,GAAI,GAAG;EACtE,MAAMgD,aAAa,GAAG3D,QAAQ,IAAI,GAAG,CAAC,CAAC;;EAEvC,oBACE5B,OAAA;IAAK8E,SAAS,EAAC,+EAA+E;IAAAC,QAAA,gBAE5F/E,OAAA;MAAK8E,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9E/E,OAAA;QAAK8E,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D/E,OAAA;UAAK8E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhD/E,OAAA;YAAK8E,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB/E,OAAA;cAAI8E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrD3D,QAAQ,CAACD;YAAI;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLnF,OAAA;cAAG8E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,EAACvD,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACiB,MAAM;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNnF,OAAA;YAAK8E,SAAS,EAAG,qFACfS,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAAR,QAAA,gBACD/E,OAAA,CAACX,OAAO;cAACyF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/BnF,OAAA;cAAA+E,QAAA,EAAOX,UAAU,CAACxC,QAAQ;YAAC;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnF,OAAA;UAAK8E,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/E,OAAA;YAAK8E,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD/E,OAAA,CAACb,MAAM,CAACqG,GAAG;cACTV,SAAS,EAAC,+DAA+D;cACzEW,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAEJ,QAAS;cAAG,CAAE;cACnCM,UAAU,EAAE;gBAAEpD,QAAQ,EAAE;cAAI;YAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnF,OAAA;YAAK8E,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9D/E,OAAA;cAAA+E,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBnF,OAAA;cAAA+E,QAAA,GAAOhC,IAAI,CAACW,KAAK,CAAC4B,QAAQ,CAAC,EAAC,GAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChH/E,OAAA,CAACZ,eAAe;QAACyG,IAAI,EAAC,MAAM;QAAAd,QAAA,eAC1B/E,OAAA,CAACb,MAAM,CAACqG,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BH,UAAU,EAAE;YAAEpD,QAAQ,EAAE;UAAI,CAAE;UAC9BsC,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAGxG/E,OAAA;YAAK8E,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrE/E,OAAA;cAAK8E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/E,OAAA;gBAAK8E,SAAS,EAAG,gDACfO,eAAe,CAACY,IAAI,KAAK,KAAK,IAAIZ,eAAe,CAACa,UAAU,KAAK,SAAS,GACtE,2BAA2B,GAC3B,6BACL,EAAE;gBAAAnB,QAAA,EACAM,eAAe,CAACY,IAAI,KAAK,KAAK,IAAIZ,eAAe,CAACa,UAAU,KAAK,SAAS,GACvE,iBAAiB,GACjB;cAAmB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EACLzD,OAAO,CAACF,oBAAoB,CAAC,iBAC5BxB,OAAA;gBAAK8E,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrE/E,OAAA,CAACR,OAAO;kBAACsF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnF,OAAA;YAAK8E,SAAS,EAAC,mCAAmC;YAACqB,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAsB,CAAE;YAAArB,QAAA,gBAC7F/E,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAI8E,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAChFM,eAAe,CAAClE,IAAI,IAAIkE,eAAe,CAACjC;cAAQ;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,EAGJE,eAAe,CAACgB,QAAQ,iBACvBrG,OAAA;gBAAK8E,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB/E,OAAA;kBAAK8E,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,gBACxF/E,OAAA;oBACEsG,GAAG,EAAEjB,eAAe,CAACgB,QAAS;oBAC9BE,GAAG,EAAC,kBAAkB;oBACtBzB,SAAS,EAAC,4DAA4D;oBACtEqB,KAAK,EAAE;sBAAEC,SAAS,EAAE;oBAAQ,CAAE;oBAC9BI,OAAO,EAAGC,CAAC,IAAK;sBACd5F,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEsE,eAAe,CAACgB,QAAQ,CAAC;sBAChEI,CAAC,CAACC,MAAM,CAACP,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/B,MAAMC,QAAQ,GAAGH,CAAC,CAACC,MAAM,CAACG,kBAAkB;sBAC5C,IAAID,QAAQ,EAAE;wBACZA,QAAQ,CAACT,KAAK,CAACQ,OAAO,GAAG,MAAM;sBACjC;oBACF,CAAE;oBACFG,MAAM,EAAEA,CAAA,KAAM;sBACZjG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuE,eAAe,CAACgB,QAAQ,CAAC;oBACrE;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFnF,OAAA;oBAAK8E,SAAS,EAAC,8EAA8E;oBAAAC,QAAA,eAC3F/E,OAAA;sBAAK8E,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1B/E,OAAA,CAACN,OAAO;wBAACoF,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5DnF,OAAA;wBAAG8E,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACpDnF,OAAA;wBAAG8E,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAC;sBAAqC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNnF,OAAA;cAAK8E,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC,CAACM,eAAe,CAACY,IAAI,KAAK,KAAK,IAAIZ,eAAe,CAACa,UAAU,KAAK,SAAS,KAAKb,eAAe,CAAC0B,OAAO;cACtG;cACAC,MAAM,CAACC,OAAO,CAAC5B,eAAe,CAAC0B,OAAO,CAAC,CAAC5D,GAAG,CAAC,CAAC,CAAC+D,GAAG,EAAEC,KAAK,CAAC,KAAK;gBAC5D,MAAMC,UAAU,GAAG1F,OAAO,CAACF,oBAAoB,CAAC,KAAK0F,GAAG;gBACxD,oBACElH,OAAA,CAACb,MAAM,CAACkI,MAAM;kBAEZjC,OAAO,EAAEA,CAAA,KAAMV,kBAAkB,CAACwC,GAAG,CAAE;kBACvCpC,SAAS,EAAG,+EACVsC,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;kBACHE,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAxC,QAAA,eAE1B/E,OAAA;oBAAK8E,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9C/E,OAAA;sBAAK8E,SAAS,EAAG,0FACfsC,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;sBAAArC,QAAA,EACAqC,UAAU,gBAAGpH,OAAA,CAACR,OAAO;wBAACsF,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG+B;oBAAG;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNnF,OAAA;sBAAM8E,SAAS,EAAC,gEAAgE;sBAAAC,QAAA,EAAEoC;oBAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F;gBAAC,GAnBD+B,GAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBK,CAAC;cAEpB,CAAC,CAAC;cAAA;cAEF;cACAnF,OAAA;gBAAK8E,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,gBAC3G/E,OAAA;kBAAK8E,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3C/E,OAAA;oBAAK8E,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjF/E,OAAA,CAACL,MAAM;sBAACmF,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACNnF,OAAA;oBAAO8E,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEvD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNnF,OAAA;kBACEmH,KAAK,EAAEzF,OAAO,CAACF,oBAAoB,CAAC,IAAI,EAAG;kBAC3CiG,QAAQ,EAAGhB,CAAC,IAAK/B,kBAAkB,CAAC+B,CAAC,CAACC,MAAM,CAACS,KAAK,CAAE;kBACpDO,WAAW,EAAC,0BAA0B;kBACtC5C,SAAS,EAAC,8KAA8K;kBACxL6C,IAAI,EAAC,GAAG;kBACRxB,KAAK,EAAE;oBAAEyB,SAAS,EAAE;kBAAQ;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACFnF,OAAA;kBAAK8E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA5HD3D,oBAAoB;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6Hf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpH/E,OAAA;QAAK8E,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpF/E,OAAA;UAAK8E,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/D/E,OAAA;YACEoF,OAAO,EAAEP,YAAa;YACtBgD,QAAQ,EAAErG,oBAAoB,KAAK,CAAE;YACrCsD,SAAS,EAAG,2HACVtD,oBAAoB,KAAK,CAAC,GACtB,8CAA8C,GAC9C,6DACL,EAAE;YAAAuD,QAAA,gBAEH/E,OAAA,CAACV,WAAW;cAACwF,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDnF,OAAA;cAAM8E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDnF,OAAA;cAAM8E,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAGTnF,OAAA;YAAK8E,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAClGzD,SAAS,CAAC6B,GAAG,CAAC,CAAC2E,CAAC,EAAEzE,KAAK,KAAK;cAC3B,MAAM0E,UAAU,GAAGrG,OAAO,CAAC2B,KAAK,CAAC,KAAK2E,SAAS,IAAItG,OAAO,CAAC2B,KAAK,CAAC,KAAK,EAAE;cACxE,MAAM4E,SAAS,GAAG5E,KAAK,KAAK7B,oBAAoB;cAChD,oBACExB,OAAA;gBAEEoF,OAAO,EAAEA,CAAA,KAAM3D,uBAAuB,CAAC4B,KAAK,CAAE;gBAC9CyB,SAAS,EAAG,sHACVmD,SAAS,GACL,kCAAkC,GAClCF,UAAU,GACV,yBAAyB,GACzB,6CACL,EAAE;gBAAAhD,QAAA,EAEFgD,UAAU,IAAI,CAACE,SAAS,gBACvBjI,OAAA,CAACR,OAAO;kBAACsF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAE/B9B,KAAK,GAAG;cACT,GAdIA,KAAK;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeJ,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnF,OAAA;YACEoF,OAAO,EAAE5D,oBAAoB,KAAKF,SAAS,CAACiB,MAAM,GAAG,CAAC,GAAGG,gBAAgB,GAAGkC,QAAS;YACrFE,SAAS,EAAC,kLAAkL;YAAAC,QAAA,gBAE5L/E,OAAA;cAAA+E,QAAA,EACGvD,oBAAoB,KAAKF,SAAS,CAACiB,MAAM,GAAG,CAAC,gBAC5CvC,OAAA,CAAAE,SAAA;gBAAA6E,QAAA,gBACE/E,OAAA;kBAAM8E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDnF,OAAA;kBAAM8E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACzC,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACN3D,oBAAoB,KAAKF,SAAS,CAACiB,MAAM,GAAG,CAAC,gBAC5CvC,OAAA,CAACJ,MAAM;cAACkF,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5CnF,OAAA,CAACT,YAAY;cAACuF,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAjfID,QAAQ;EAAA,QACGpB,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAAiJ,EAAA,GAHxB/H,QAAQ;AAmfd,eAAeA,QAAQ;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\AuthDebug.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthDebug = () => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const token = localStorage.getItem('token');\n  const userFromStorage = localStorage.getItem('user');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '10px',\n      right: '10px',\n      background: 'white',\n      padding: '10px',\n      border: '1px solid #ccc',\n      borderRadius: '5px',\n      fontSize: '12px',\n      maxWidth: '300px',\n      zIndex: 9999\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"Auth Debug Info\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Redux User:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 10\n      }, this), \" \", user ? `${user.name} (${user.email})` : 'Not logged in']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Token:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 10\n      }, this), \" \", token ? 'Present' : 'Missing']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"User Storage:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 10\n      }, this), \" \", userFromStorage ? 'Present' : 'Missing']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"User ID:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 14\n        }, this), \" \", user._id]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Level:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 14\n        }, this), \" \", user.level]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Class:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 14\n        }, this), \" \", user.class]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Admin:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 14\n        }, this), \" \", user.isAdmin ? 'Yes' : 'No']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthDebug, \"HA6wZhGeXNR9tzJ3aDGGsGCdOyI=\", false, function () {\n  return [useSelector];\n});\n_c = AuthDebug;\nexport default AuthDebug;\nvar _c;\n$RefreshReg$(_c, \"AuthDebug\");", "map": {"version": 3, "names": ["React", "useSelector", "jsxDEV", "_jsxDEV", "AuthDebug", "_s", "user", "state", "token", "localStorage", "getItem", "userFromStorage", "style", "position", "top", "right", "background", "padding", "border", "borderRadius", "fontSize", "max<PERSON><PERSON><PERSON>", "zIndex", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "email", "_id", "level", "class", "isAdmin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/AuthDebug.js"], "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\n\nconst AuthDebug = () => {\n  const { user } = useSelector((state) => state.user);\n  const token = localStorage.getItem('token');\n  const userFromStorage = localStorage.getItem('user');\n\n  return (\n    <div style={{ \n      position: 'fixed', \n      top: '10px', \n      right: '10px', \n      background: 'white', \n      padding: '10px', \n      border: '1px solid #ccc',\n      borderRadius: '5px',\n      fontSize: '12px',\n      maxWidth: '300px',\n      zIndex: 9999\n    }}>\n      <h4>Auth Debug Info</h4>\n      <p><strong>Redux User:</strong> {user ? `${user.name} (${user.email})` : 'Not logged in'}</p>\n      <p><strong>Token:</strong> {token ? 'Present' : 'Missing'}</p>\n      <p><strong>User Storage:</strong> {userFromStorage ? 'Present' : 'Missing'}</p>\n      {user && (\n        <div>\n          <p><strong>User ID:</strong> {user._id}</p>\n          <p><strong>Level:</strong> {user.level}</p>\n          <p><strong>Class:</strong> {user.class}</p>\n          <p><strong>Admin:</strong> {user.isAdmin ? 'Yes' : 'No'}</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AuthDebug;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGL,WAAW,CAAEM,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,eAAe,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAEpD,oBACEP,OAAA;IAAKS,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,gBAAgB;MACxBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,gBACApB,OAAA;MAAAoB,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxBxB,OAAA;MAAAoB,QAAA,gBAAGpB,OAAA;QAAAoB,QAAA,EAAQ;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACrB,IAAI,GAAI,GAAEA,IAAI,CAACsB,IAAK,KAAItB,IAAI,CAACuB,KAAM,GAAE,GAAG,eAAe;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7FxB,OAAA;MAAAoB,QAAA,gBAAGpB,OAAA;QAAAoB,QAAA,EAAQ;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACnB,KAAK,GAAG,SAAS,GAAG,SAAS;IAAA;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9DxB,OAAA;MAAAoB,QAAA,gBAAGpB,OAAA;QAAAoB,QAAA,EAAQ;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAAChB,eAAe,GAAG,SAAS,GAAG,SAAS;IAAA;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAC9ErB,IAAI,iBACHH,OAAA;MAAAoB,QAAA,gBACEpB,OAAA;QAAAoB,QAAA,gBAAGpB,OAAA;UAAAoB,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACrB,IAAI,CAACwB,GAAG;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CxB,OAAA;QAAAoB,QAAA,gBAAGpB,OAAA;UAAAoB,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACrB,IAAI,CAACyB,KAAK;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CxB,OAAA;QAAAoB,QAAA,gBAAGpB,OAAA;UAAAoB,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACrB,IAAI,CAAC0B,KAAK;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CxB,OAAA;QAAAoB,QAAA,gBAAGpB,OAAA;UAAAoB,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACrB,IAAI,CAAC2B,OAAO,GAAG,KAAK,GAAG,IAAI;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtB,EAAA,CAhCID,SAAS;EAAA,QACIH,WAAW;AAAA;AAAAiC,EAAA,GADxB9B,SAAS;AAkCf,eAAeA,SAAS;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
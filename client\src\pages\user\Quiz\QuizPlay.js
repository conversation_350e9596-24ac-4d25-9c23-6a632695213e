import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { message } from 'antd';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Tb<PERSON>lock, 
  TbArrowLeft, 
  TbArrowRight, 
  TbCheck, 
  TbX,
  TbPhoto,
  TbEdit,
  TbFlag
} from 'react-icons/tb';
import { getExamById } from '../../../apicalls/exams';
import { addReport } from '../../../apicalls/reports';
import './responsive.css';
import './quiz-improvements.css';

const QuizPlay = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);

  // Quiz state
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default
  const [startTime, setStartTime] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize user from localStorage if not in Redux
  useEffect(() => {
    if (!user) {
      const storedUser = localStorage.getItem('user');
      const token = localStorage.getItem('token');
      
      if (!token) {
        console.log('No token found, redirecting to login');
        message.error('Please login to access quizzes');
        navigate('/login');
        return;
      }
      
      if (storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          console.log('QuizPlay: User found in localStorage:', userData.name);
          // The ProtectedRoute will handle setting the user in Redux
        } catch (error) {
          console.error('Error parsing stored user data:', error);
          navigate('/login');
        }
      }
    }
  }, [user, navigate]);

  // Load quiz data
  useEffect(() => {
    const loadQuizData = async () => {
      try {
        setLoading(true);
        console.log('Loading quiz data for ID:', id);
        
        const response = await getExamById({ examId: id });
        console.log('Quiz API response:', response);
        
        if (response.success) {
          if (!response.data) {
            message.error('Quiz data not found');
            navigate('/quiz');
            return;
          }
          
          if (!response.data.questions || response.data.questions.length === 0) {
            message.error('This quiz has no questions available');
            navigate('/quiz');
            return;
          }
          
          setExamData(response.data);
          setQuestions(response.data.questions || []);
          setTimeLeft((response.data.duration || 30) * 60);
          setStartTime(new Date());
        } else {
          console.error('Quiz API error:', response.message);
          message.error(response.message || 'Failed to load quiz');
          navigate('/quiz');
        }
      } catch (error) {
        console.error('Quiz loading error:', error);
        message.error('Failed to load quiz. Please try again.');
        navigate('/quiz');
      } finally {
        setLoading(false);
      }
    };

    if (id && user) {
      loadQuizData();
    }
  }, [id, navigate, user]);

  // Submit quiz function
  const handleSubmitQuiz = useCallback(async () => {
    try {
      // Get user data from Redux or localStorage
      let currentUser = user;
      if (!currentUser || !currentUser._id) {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            currentUser = JSON.parse(storedUser);
          } catch (error) {
            console.error('Error parsing stored user data:', error);
          }
        }
      }

      if (!currentUser || !currentUser._id) {
        message.error('User session expired. Please login again.');
        navigate('/login');
        return;
      }

      const endTime = new Date();
      const timeTaken = Math.floor((endTime - startTime) / 1000);

      // Calculate results
      let correctAnswers = 0;
      const resultDetails = questions.map((question, index) => {
        const userAnswer = answers[index];
        const isCorrect = userAnswer === question.correctAnswer;
        if (isCorrect) correctAnswers++;

        return {
          question: question._id,
          userAnswer,
          correctAnswer: question.correctAnswer,
          isCorrect
        };
      });

      const percentage = Math.round((correctAnswers / questions.length) * 100);
      const verdict = percentage >= 60 ? 'Pass' : 'Fail'; // 60% pass mark

      // Submit to backend
      const reportData = {
        exam: id,
        user: currentUser._id,
        result: {
          correctAnswers,
          wrongAnswers: questions.length - correctAnswers,
          percentage,
          score: percentage, // Add score field for backend compatibility
          verdict: verdict, // Add verdict field for backend compatibility
          timeTaken,
          points: correctAnswers * 10 // Add points calculation (10 points per correct answer)
        }
      };

      console.log('Submitting quiz result:', reportData);
      await addReport(reportData);

      // Navigate to results
      navigate(`/quiz/${id}/result`, {
        state: {
          percentage,
          correctAnswers,
          totalQuestions: questions.length,
          timeTaken,
          resultDetails
        }
      });
    } catch (error) {
      message.error('Failed to submit quiz');
    }
  }, [startTime, questions, answers, id, navigate, user]);

  // Timer countdown
  useEffect(() => {
    if (timeLeft <= 0) {
      handleSubmitQuiz();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, handleSubmitQuiz]);

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle answer selection
  const handleAnswerSelect = (answer) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestionIndex]: answer
    }));
  };

  // Navigation functions
  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Early return if user is null to prevent errors
  if (user === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Checking authentication...</p>
        </div>
      </div>
    );
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">
            {!user ? 'Checking authentication...' : 'Loading quiz...'}
          </p>
        </div>
      </div>
    );
  }

  if (!questions.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 font-medium">No questions available for this quiz.</p>
          <button 
            onClick={() => navigate('/quiz')}
            className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
  const isTimeWarning = timeLeft <= 300; // 5 minutes warning

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe">
      {/* Enhanced Header with better mobile support */}
      <div className="bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Quiz Info */}
            <div className="flex items-center gap-3">
              <button
                onClick={() => navigate('/quiz')}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <TbX className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-gray-900">
                  {examData?.name || 'Quiz'}
                </h1>
                <p className="text-xs sm:text-sm text-gray-600">
                  Question {currentQuestionIndex + 1} of {questions.length}
                </p>
              </div>
            </div>

            {/* Timer */}
            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
              isTimeWarning ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'
            }`}>
              <TbClock className="w-4 h-4" />
              <span className="font-mono font-bold text-sm">
                {formatTime(timeLeft)}
              </span>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-3">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content with better mobile support */}
      <div className="max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestionIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape"
          >
            {/* Question Content - Fixed scrolling and responsive design */}
            <div className="p-4 sm:p-6 flex-1 overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
              <div className="mb-6">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed">
                  {currentQuestion.name || currentQuestion.question}
                </h2>
                
                {/* Enhanced Image Display with better error handling */}
                {currentQuestion.imageUrl && (
                  <div className="mb-6">
                    <div className="relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4">
                      <img 
                        src={currentQuestion.imageUrl} 
                        alt="Question diagram"
                        className="w-full max-w-full mx-auto h-auto object-contain rounded-lg"
                        style={{ maxHeight: '300px' }}
                        onError={(e) => {
                          console.error('Image failed to load:', currentQuestion.imageUrl);
                          e.target.style.display = 'none';
                          const errorDiv = e.target.nextElementSibling;
                          if (errorDiv) {
                            errorDiv.style.display = 'flex';
                          }
                        }}
                        onLoad={() => {
                          console.log('Image loaded successfully:', currentQuestion.imageUrl);
                        }}
                      />
                      <div className="hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg">
                        <div className="text-center">
                          <TbPhoto className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                          <p className="text-sm">Image could not be loaded</p>
                          <p className="text-xs text-gray-400 mt-1">Please check your internet connection</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Enhanced Answer Options with better responsive design */}
              <div className="space-y-3 sm:space-y-4">
                {(currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ? (
                  // Multiple Choice Questions - Enhanced for mobile
                  Object.entries(currentQuestion.options).map(([key, value]) => {
                    const isSelected = answers[currentQuestionIndex] === key;
                    return (
                      <motion.button
                        key={key}
                        onClick={() => handleAnswerSelect(key)}
                        className={`w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 shadow-md'
                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'
                        }`}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <div className="flex items-start gap-3 sm:gap-4">
                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${
                            isSelected
                              ? 'border-blue-500 bg-blue-500 text-white'
                              : 'border-gray-300 text-gray-600'
                          }`}>
                            {isSelected ? <TbCheck className="w-4 h-4 sm:w-5 sm:h-5" /> : key}
                          </div>
                          <span className="text-gray-900 font-medium text-sm sm:text-base leading-relaxed">{value}</span>
                        </div>
                      </motion.button>
                    );
                  })
                ) : (
                  // Enhanced Fill-in-the-Blank Questions with better spacing
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <TbEdit className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                      </div>
                      <label className="text-sm font-semibold text-gray-700">
                        Your Answer:
                      </label>
                    </div>
                    <textarea
                      value={answers[currentQuestionIndex] || ''}
                      onChange={(e) => handleAnswerSelect(e.target.value)}
                      placeholder="Type your answer here..."
                      className="w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base"
                      rows="4"
                      style={{ minHeight: '100px' }}
                    />
                    <div className="mt-2 text-xs text-gray-500">
                      Tip: Take your time to write a clear and complete answer
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Enhanced Navigation Footer - Mobile Responsive */}
      <div className="bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe">
        <div className="max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape">
          <div className="flex items-center justify-between gap-2 sm:gap-4">
            {/* Previous Button - Mobile Optimized */}
            <button
              onClick={goToPrevious}
              disabled={currentQuestionIndex === 0}
              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${
                currentQuestionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'
              }`}
            >
              <TbArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="hidden sm:inline">Previous</span>
              <span className="sm:hidden">Prev</span>
            </button>

            {/* Question Navigation Dots - Enhanced for mobile */}
            <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide">
              {questions.map((_, index) => {
                const isAnswered = answers[index] !== undefined && answers[index] !== '';
                const isCurrent = index === currentQuestionIndex;
                return (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestionIndex(index)}
                    className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${
                      isCurrent
                        ? 'bg-blue-600 text-white border-2 border-blue-600'
                        : isAnswered
                        ? 'bg-green-500 text-white border-2 border-green-500'
                        : 'bg-gray-200 text-gray-600 border-2 border-gray-300 hover:bg-gray-300'
                    }`}
                  >
                    {index + 1}
                  </button>
                );
              })}
            </div>

            {/* Next/Submit Button - Mobile Optimized */}
            <button
              onClick={currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext}
              className="flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base"
            >
              <span>
                {currentQuestionIndex === questions.length - 1 ? (
                  <>
                    <span className="hidden sm:inline">Submit Quiz</span>
                    <span className="sm:hidden">Submit</span>
                  </>
                ) : (
                  'Next'
                )}
              </span>
              {currentQuestionIndex === questions.length - 1 ? (
                <TbFlag className="w-4 h-4 sm:w-5 sm:h-5" />
              ) : (
                <TbArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizPlay;

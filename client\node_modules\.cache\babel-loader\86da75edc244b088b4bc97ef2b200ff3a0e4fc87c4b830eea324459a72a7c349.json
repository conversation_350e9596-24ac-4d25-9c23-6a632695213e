{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbFlag } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\nimport './quiz-improvements.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Initialize user from localStorage if not in Redux\n  useEffect(() => {\n    if (!user) {\n      const storedUser = localStorage.getItem('user');\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('No token found, redirecting to login');\n        message.error('Please login to access quizzes');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      if (storedUser) {\n        try {\n          const userData = JSON.parse(storedUser);\n          console.log('QuizPlay: User found in localStorage:', userData.name);\n          // The ProtectedRoute will handle setting the user in Redux\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          startTransition(() => {\n            navigate('/login');\n          });\n        }\n      }\n    }\n  }, [user, navigate]);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz data for ID:', id);\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      // Get user data from Redux or localStorage\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail'; // 60% pass mark\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          // Add score field for backend compatibility\n          verdict: verdict,\n          // Add verdict field for backend compatibility\n          timeTaken,\n          points: correctAnswers * 10 // Add points calculation (10 points per correct answer)\n        }\n      };\n\n      console.log('Submitting quiz result:', reportData);\n      await addReport(reportData);\n\n      // Navigate to results\n      startTransition(() => {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails\n          }\n        });\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Early return if user is null to prevent errors\n  if (user === null) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Checking authentication...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading || !user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: !user ? 'Checking authentication...' : 'Loading quiz...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this);\n  }\n  if (!questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"No questions available for this quiz.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/quiz'),\n          className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = (currentQuestionIndex + 1) / questions.length * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/quiz'),\n              className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-5 h-5 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-lg sm:text-xl font-bold text-gray-900\",\n                children: (examData === null || examData === void 0 ? void 0 : examData.name) || 'Quiz'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs sm:text-sm text-gray-600\",\n                children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-3 py-2 rounded-lg ${isTimeWarning ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-mono font-bold text-sm\",\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 sm:p-6 flex-1 overflow-y-auto\",\n            style: {\n              maxHeight: 'calc(100vh - 280px)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                children: currentQuestion.name || currentQuestion.question\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), currentQuestion.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentQuestion.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"w-full max-w-full mx-auto h-auto object-contain rounded-lg\",\n                    style: {\n                      maxHeight: '300px'\n                    },\n                    onError: e => {\n                      console.error('Image failed to load:', currentQuestion.imageUrl);\n                      e.target.style.display = 'none';\n                      const errorDiv = e.target.nextElementSibling;\n                      if (errorDiv) {\n                        errorDiv.style.display = 'flex';\n                      }\n                    },\n                    onLoad: () => {\n                      console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                        className: \"w-12 h-12 mx-auto mb-2 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: \"Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 375,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: \"Please check your internet connection\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 376,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: (currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ?\n              // Multiple Choice Questions - Enhanced for mobile\n              Object.entries(currentQuestion.options).map(([key, value]) => {\n                const isSelected = answers[currentQuestionIndex] === key;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(key),\n                  className: `w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                  whileHover: {\n                    scale: 1.01\n                  },\n                  whileTap: {\n                    scale: 0.99\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start gap-3 sm:gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 43\n                      }, this) : key\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 25\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 23\n                }, this);\n              }) :\n              /*#__PURE__*/\n              // Enhanced Fill-in-the-Blank Questions with better spacing\n              _jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-sm font-semibold text-gray-700\",\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: answers[currentQuestionIndex] || '',\n                  onChange: e => handleAnswerSelect(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\",\n                  rows: \"4\",\n                  style: {\n                    minHeight: '100px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-xs text-gray-500\",\n                  children: \"Tip: Take your time to write a clear and complete answer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, currentQuestionIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestionIndex === 0,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${currentQuestionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\",\n            children: questions.map((_, index) => {\n              const isAnswered = answers[index] !== undefined && answers[index] !== '';\n              const isCurrent = index === currentQuestionIndex;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentQuestionIndex(index),\n                className: `w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${isCurrent ? 'bg-blue-600 text-white border-2 border-blue-600' : isAnswered ? 'bg-green-500 text-white border-2 border-green-500' : 'bg-gray-200 text-gray-600 border-2 border-gray-300 hover:bg-gray-300'}`,\n                children: index + 1\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext,\n            className: \"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"Submit Quiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sm:hidden\",\n                  children: \"Submit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(TbFlag, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"h14qSvT8jLUc8uxuzWpa8Vky7rE=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "message", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbFlag", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizPlay", "_s", "id", "navigate", "user", "state", "examData", "setExamData", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loading", "setLoading", "storedUser", "localStorage", "getItem", "token", "console", "log", "error", "userData", "JSON", "parse", "name", "loadQuizData", "response", "examId", "success", "data", "length", "duration", "Date", "handleSubmitQuiz", "currentUser", "_id", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "percentage", "round", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "points", "totalQuestions", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "answer", "goToNext", "goToPrevious", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "progress", "isTimeWarning", "style", "width", "mode", "div", "initial", "opacity", "x", "animate", "exit", "transition", "maxHeight", "imageUrl", "src", "alt", "onError", "e", "target", "display", "errorDiv", "nextElement<PERSON><PERSON>ling", "onLoad", "type", "answerType", "options", "Object", "entries", "key", "value", "isSelected", "button", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "minHeight", "disabled", "_", "isAnswered", "undefined", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Tb<PERSON>lock, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck, \n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbFlag\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\nimport './quiz-improvements.css';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Initialize user from localStorage if not in Redux\n  useEffect(() => {\n    if (!user) {\n      const storedUser = localStorage.getItem('user');\n      const token = localStorage.getItem('token');\n      \n      if (!token) {\n        console.log('No token found, redirecting to login');\n        message.error('Please login to access quizzes');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      \n      if (storedUser) {\n        try {\n          const userData = JSON.parse(storedUser);\n          console.log('QuizPlay: User found in localStorage:', userData.name);\n          // The ProtectedRoute will handle setting the user in Redux\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          startTransition(() => {\n            navigate('/login');\n          });\n        }\n      }\n    }\n  }, [user, navigate]);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz data for ID:', id);\n        \n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      // Get user data from Redux or localStorage\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail'; // 60% pass mark\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage, // Add score field for backend compatibility\n          verdict: verdict, // Add verdict field for backend compatibility\n          timeTaken,\n          points: correctAnswers * 10 // Add points calculation (10 points per correct answer)\n        }\n      };\n\n      console.log('Submitting quiz result:', reportData);\n      await addReport(reportData);\n\n      // Navigate to results\n      startTransition(() => {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails\n          }\n        });\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Early return if user is null to prevent errors\n  if (user === null) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Checking authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading || !user) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">\n            {!user ? 'Checking authentication...' : 'Loading quiz...'}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-600 font-medium\">No questions available for this quiz.</p>\n          <button \n            onClick={() => navigate('/quiz')}\n            className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\">\n      {/* Enhanced Header with better mobile support */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Info */}\n            <div className=\"flex items-center gap-3\">\n              <button\n                onClick={() => navigate('/quiz')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <TbX className=\"w-5 h-5 text-gray-600\" />\n              </button>\n              <div>\n                <h1 className=\"text-lg sm:text-xl font-bold text-gray-900\">\n                  {examData?.name || 'Quiz'}\n                </h1>\n                <p className=\"text-xs sm:text-sm text-gray-600\">\n                  Question {currentQuestionIndex + 1} of {questions.length}\n                </p>\n              </div>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${\n              isTimeWarning ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-4 h-4\" />\n              <span className=\"font-mono font-bold text-sm\">\n                {formatTime(timeLeft)}\n              </span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-3\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${progress}%` }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Main Content with better mobile support */}\n      <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\"\n          >\n            {/* Question Content - Fixed scrolling and responsive design */}\n            <div className=\"p-4 sm:p-6 flex-1 overflow-y-auto\" style={{ maxHeight: 'calc(100vh - 280px)' }}>\n              <div className=\"mb-6\">\n                <h2 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                  {currentQuestion.name || currentQuestion.question}\n                </h2>\n                \n                {/* Enhanced Image Display with better error handling */}\n                {currentQuestion.imageUrl && (\n                  <div className=\"mb-6\">\n                    <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\">\n                      <img \n                        src={currentQuestion.imageUrl} \n                        alt=\"Question diagram\"\n                        className=\"w-full max-w-full mx-auto h-auto object-contain rounded-lg\"\n                        style={{ maxHeight: '300px' }}\n                        onError={(e) => {\n                          console.error('Image failed to load:', currentQuestion.imageUrl);\n                          e.target.style.display = 'none';\n                          const errorDiv = e.target.nextElementSibling;\n                          if (errorDiv) {\n                            errorDiv.style.display = 'flex';\n                          }\n                        }}\n                        onLoad={() => {\n                          console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                        }}\n                      />\n                      <div className=\"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\">\n                        <div className=\"text-center\">\n                          <TbPhoto className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                          <p className=\"text-sm\">Image could not be loaded</p>\n                          <p className=\"text-xs text-gray-400 mt-1\">Please check your internet connection</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Enhanced Answer Options with better responsive design */}\n              <div className=\"space-y-3 sm:space-y-4\">\n                {(currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ? (\n                  // Multiple Choice Questions - Enhanced for mobile\n                  Object.entries(currentQuestion.options).map(([key, value]) => {\n                    const isSelected = answers[currentQuestionIndex] === key;\n                    return (\n                      <motion.button\n                        key={key}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 shadow-md'\n                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                        }`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <div className=\"flex items-start gap-3 sm:gap-4\">\n                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" /> : key}\n                          </div>\n                          <span className=\"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\">{value}</span>\n                        </div>\n                      </motion.button>\n                    );\n                  })\n                ) : (\n                  // Enhanced Fill-in-the-Blank Questions with better spacing\n                  <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\">\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                        <TbEdit className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                      </div>\n                      <label className=\"text-sm font-semibold text-gray-700\">\n                        Your Answer:\n                      </label>\n                    </div>\n                    <textarea\n                      value={answers[currentQuestionIndex] || ''}\n                      onChange={(e) => handleAnswerSelect(e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\"\n                      rows=\"4\"\n                      style={{ minHeight: '100px' }}\n                    />\n                    <div className=\"mt-2 text-xs text-gray-500\">\n                      Tip: Take your time to write a clear and complete answer\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Enhanced Navigation Footer - Mobile Responsive */}\n      <div className=\"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Previous Button - Mobile Optimized */}\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${\n                currentQuestionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n              <span className=\"sm:hidden\">Prev</span>\n            </button>\n\n            {/* Question Navigation Dots - Enhanced for mobile */}\n            <div className=\"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\">\n              {questions.map((_, index) => {\n                const isAnswered = answers[index] !== undefined && answers[index] !== '';\n                const isCurrent = index === currentQuestionIndex;\n                return (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentQuestionIndex(index)}\n                    className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${\n                      isCurrent\n                        ? 'bg-blue-600 text-white border-2 border-blue-600'\n                        : isAnswered\n                        ? 'bg-green-500 text-white border-2 border-green-500'\n                        : 'bg-gray-200 text-gray-600 border-2 border-gray-300 hover:bg-gray-300'\n                    }`}\n                  >\n                    {index + 1}\n                  </button>\n                );\n              })}\n            </div>\n\n            {/* Next/Submit Button - Mobile Optimized */}\n            <button\n              onClick={currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext}\n              className=\"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\"\n            >\n              <span>\n                {currentQuestionIndex === questions.length - 1 ? (\n                  <>\n                    <span className=\"hidden sm:inline\">Submit Quiz</span>\n                    <span className=\"sm:hidden\">Submit</span>\n                  </>\n                ) : (\n                  'Next'\n                )}\n              </span>\n              {currentQuestionIndex === questions.length - 1 ? (\n                <TbFlag className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              ) : (\n                <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAO,kBAAkB;AACzB,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGtB,SAAS,CAAC,CAAC;EAC1B,MAAMuB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,IAAI,EAAE;MACT,MAAMgB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,MAAMC,KAAK,GAAGF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAI,CAACC,KAAK,EAAE;QACVC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD1C,OAAO,CAAC2C,KAAK,CAAC,gCAAgC,CAAC;QAC/C/C,eAAe,CAAC,MAAM;UACpBwB,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,IAAIiB,UAAU,EAAE;QACd,IAAI;UACF,MAAMO,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACT,UAAU,CAAC;UACvCI,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEE,QAAQ,CAACG,IAAI,CAAC;UACnE;QACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD/C,eAAe,CAAC,MAAM;YACpBwB,QAAQ,CAAC,QAAQ,CAAC;UACpB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE,CAACC,IAAI,EAAED,QAAQ,CAAC,CAAC;;EAEpB;EACA1B,SAAS,CAAC,MAAM;IACd,MAAMsD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFZ,UAAU,CAAC,IAAI,CAAC;QAChBK,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEvB,EAAE,CAAC;QAE5C,MAAM8B,QAAQ,GAAG,MAAMtC,WAAW,CAAC;UAAEuC,MAAM,EAAE/B;QAAG,CAAC,CAAC;QAClDsB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClBpD,OAAO,CAAC2C,KAAK,CAAC,qBAAqB,CAAC;YACpC/C,eAAe,CAAC,MAAM;cACpBwB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAAC6B,QAAQ,CAACG,IAAI,CAAC3B,SAAS,IAAIwB,QAAQ,CAACG,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,KAAK,CAAC,EAAE;YACpErD,OAAO,CAAC2C,KAAK,CAAC,sCAAsC,CAAC;YACrD/C,eAAe,CAAC,MAAM;cACpBwB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAI,WAAW,CAACyB,QAAQ,CAACG,IAAI,CAAC;UAC1B1B,YAAY,CAACuB,QAAQ,CAACG,IAAI,CAAC3B,SAAS,IAAI,EAAE,CAAC;UAC3CO,WAAW,CAAC,CAACiB,QAAQ,CAACG,IAAI,CAACE,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC;UAChDpB,YAAY,CAAC,IAAIqB,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC,MAAM;UACLd,OAAO,CAACE,KAAK,CAAC,iBAAiB,EAAEM,QAAQ,CAACjD,OAAO,CAAC;UAClDA,OAAO,CAAC2C,KAAK,CAACM,QAAQ,CAACjD,OAAO,IAAI,qBAAqB,CAAC;UACxDJ,eAAe,CAAC,MAAM;YACpBwB,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C3C,OAAO,CAAC2C,KAAK,CAAC,wCAAwC,CAAC;QACvD/C,eAAe,CAAC,MAAM;UACpBwB,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRgB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIjB,EAAE,IAAIE,IAAI,EAAE;MACd2B,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC7B,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMmC,gBAAgB,GAAG7D,WAAW,CAAC,YAAY;IAC/C,IAAI;MACF;MACA,IAAI8D,WAAW,GAAGpC,IAAI;MACtB,IAAI,CAACoC,WAAW,IAAI,CAACA,WAAW,CAACC,GAAG,EAAE;QACpC,MAAMrB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIF,UAAU,EAAE;UACd,IAAI;YACFoB,WAAW,GAAGZ,IAAI,CAACC,KAAK,CAACT,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOM,KAAK,EAAE;YACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACzD;QACF;MACF;MAEA,IAAI,CAACc,WAAW,IAAI,CAACA,WAAW,CAACC,GAAG,EAAE;QACpC1D,OAAO,CAAC2C,KAAK,CAAC,2CAA2C,CAAC;QAC1D/C,eAAe,CAAC,MAAM;UACpBwB,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAMuC,OAAO,GAAG,IAAIJ,IAAI,CAAC,CAAC;MAC1B,MAAMK,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAG1B,SAAS,IAAI,IAAI,CAAC;;MAE1D;MACA,IAAI8B,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGvC,SAAS,CAACwC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGvC,OAAO,CAACsC,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLG,QAAQ,EAAEA,QAAQ,CAACR,GAAG;UACtBU,UAAU;UACVE,aAAa,EAAEJ,QAAQ,CAACI,aAAa;UACrCD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAME,UAAU,GAAGV,IAAI,CAACW,KAAK,CAAET,cAAc,GAAGtC,SAAS,CAAC4B,MAAM,GAAI,GAAG,CAAC;MACxE,MAAMoB,OAAO,GAAGF,UAAU,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;;MAEpD;MACA,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAExD,EAAE;QACRE,IAAI,EAAEoC,WAAW,CAACC,GAAG;QACrBkB,MAAM,EAAE;UACNb,cAAc;UACdc,YAAY,EAAEpD,SAAS,CAAC4B,MAAM,GAAGU,cAAc;UAC/CQ,UAAU;UACVO,KAAK,EAAEP,UAAU;UAAE;UACnBE,OAAO,EAAEA,OAAO;UAAE;UAClBb,SAAS;UACTmB,MAAM,EAAEhB,cAAc,GAAG,EAAE,CAAC;QAC9B;MACF,CAAC;;MAEDtB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgC,UAAU,CAAC;MAClD,MAAM9D,SAAS,CAAC8D,UAAU,CAAC;;MAE3B;MACA9E,eAAe,CAAC,MAAM;QACpBwB,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAC7BG,KAAK,EAAE;YACLiD,UAAU;YACVR,cAAc;YACdiB,cAAc,EAAEvD,SAAS,CAAC4B,MAAM;YAChCO,SAAS;YACTI;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC,EAAE,CAACV,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEV,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEvD;EACA3B,SAAS,CAAC,MAAM;IACd,IAAIqC,QAAQ,IAAI,CAAC,EAAE;MACjByB,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMyB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BlD,WAAW,CAACmD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAAClD,QAAQ,EAAEyB,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAM6B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAG1B,IAAI,CAACC,KAAK,CAACwB,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrC9D,UAAU,CAACqD,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACxD,oBAAoB,GAAGiE;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIlE,oBAAoB,GAAGF,SAAS,CAAC4B,MAAM,GAAG,CAAC,EAAE;MAC/CzB,uBAAuB,CAACuD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInE,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACuD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,IAAI9D,IAAI,KAAK,IAAI,EAAE;IACjB,oBACEP,OAAA;MAAKiF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA;UAAKiF,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGtF,OAAA;UAAGiF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjE,OAAO,IAAI,CAACd,IAAI,EAAE;IACpB,oBACEP,OAAA;MAAKiF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA;UAAKiF,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGtF,OAAA;UAAGiF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACrC,CAAC3E,IAAI,GAAG,4BAA4B,GAAG;QAAiB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC3E,SAAS,CAAC4B,MAAM,EAAE;IACrB,oBACEvC,OAAA;MAAKiF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA;UAAGiF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClFtF,OAAA;UACEuF,OAAO,EAAEA,CAAA,KAAMjF,QAAQ,CAAC,OAAO,CAAE;UACjC2E,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAC/E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAG7E,SAAS,CAACE,oBAAoB,CAAC;EACvD,MAAM4E,QAAQ,GAAI,CAAC5E,oBAAoB,GAAG,CAAC,IAAIF,SAAS,CAAC4B,MAAM,GAAI,GAAG;EACtE,MAAMmD,aAAa,GAAGzE,QAAQ,IAAI,GAAG,CAAC,CAAC;;EAEvC,oBACEjB,OAAA;IAAKiF,SAAS,EAAC,+EAA+E;IAAAC,QAAA,gBAE5FlF,OAAA;MAAKiF,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9ElF,OAAA;QAAKiF,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DlF,OAAA;UAAKiF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhDlF,OAAA;YAAKiF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClF,OAAA;cACEuF,OAAO,EAAEA,CAAA,KAAMjF,QAAQ,CAAC,OAAO,CAAE;cACjC2E,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eAE9DlF,OAAA,CAACP,GAAG;gBAACwF,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACTtF,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAIiF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD,CAAAzE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,IAAI,KAAI;cAAM;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACLtF,OAAA;gBAAGiF,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,WACrC,EAACrE,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAAC4B,MAAM;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAKiF,SAAS,EAAG,gDACfS,aAAa,GAAG,yBAAyB,GAAG,2BAC7C,EAAE;YAAAR,QAAA,gBACDlF,OAAA,CAACX,OAAO;cAAC4F,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/BtF,OAAA;cAAMiF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAC1CX,UAAU,CAACtD,QAAQ;YAAC;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtF,OAAA;UAAKiF,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBlF,OAAA;YAAKiF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDlF,OAAA;cACEiF,SAAS,EAAC,0DAA0D;cACpEU,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAEH,QAAS;cAAG;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKiF,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChHlF,OAAA,CAACZ,eAAe;QAACyG,IAAI,EAAC,MAAM;QAAAX,QAAA,eAC1BlF,OAAA,CAACb,MAAM,CAAC2G,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BG,UAAU,EAAE;YAAE5D,QAAQ,EAAE;UAAI,CAAE;UAC9ByC,SAAS,EAAC,8FAA8F;UAAAC,QAAA,eAGxGlF,OAAA;YAAKiF,SAAS,EAAC,mCAAmC;YAACU,KAAK,EAAE;cAAEU,SAAS,EAAE;YAAsB,CAAE;YAAAnB,QAAA,gBAC7FlF,OAAA;cAAKiF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlF,OAAA;gBAAIiF,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAChFM,eAAe,CAACvD,IAAI,IAAIuD,eAAe,CAACpC;cAAQ;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,EAGJE,eAAe,CAACc,QAAQ,iBACvBtG,OAAA;gBAAKiF,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBlF,OAAA;kBAAKiF,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,gBACxFlF,OAAA;oBACEuG,GAAG,EAAEf,eAAe,CAACc,QAAS;oBAC9BE,GAAG,EAAC,kBAAkB;oBACtBvB,SAAS,EAAC,4DAA4D;oBACtEU,KAAK,EAAE;sBAAEU,SAAS,EAAE;oBAAQ,CAAE;oBAC9BI,OAAO,EAAGC,CAAC,IAAK;sBACd/E,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAE2D,eAAe,CAACc,QAAQ,CAAC;sBAChEI,CAAC,CAACC,MAAM,CAAChB,KAAK,CAACiB,OAAO,GAAG,MAAM;sBAC/B,MAAMC,QAAQ,GAAGH,CAAC,CAACC,MAAM,CAACG,kBAAkB;sBAC5C,IAAID,QAAQ,EAAE;wBACZA,QAAQ,CAAClB,KAAK,CAACiB,OAAO,GAAG,MAAM;sBACjC;oBACF,CAAE;oBACFG,MAAM,EAAEA,CAAA,KAAM;sBACZpF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4D,eAAe,CAACc,QAAQ,CAAC;oBACrE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFtF,OAAA;oBAAKiF,SAAS,EAAC,8EAA8E;oBAAAC,QAAA,eAC3FlF,OAAA;sBAAKiF,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BlF,OAAA,CAACN,OAAO;wBAACuF,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5DtF,OAAA;wBAAGiF,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACpDtF,OAAA;wBAAGiF,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAC;sBAAqC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC,CAACM,eAAe,CAACwB,IAAI,KAAK,KAAK,IAAIxB,eAAe,CAACyB,UAAU,KAAK,SAAS,KAAKzB,eAAe,CAAC0B,OAAO;cACtG;cACAC,MAAM,CAACC,OAAO,CAAC5B,eAAe,CAAC0B,OAAO,CAAC,CAAC/D,GAAG,CAAC,CAAC,CAACkE,GAAG,EAAEC,KAAK,CAAC,KAAK;gBAC5D,MAAMC,UAAU,GAAGxG,OAAO,CAACF,oBAAoB,CAAC,KAAKwG,GAAG;gBACxD,oBACErH,OAAA,CAACb,MAAM,CAACqI,MAAM;kBAEZjC,OAAO,EAAEA,CAAA,KAAMV,kBAAkB,CAACwC,GAAG,CAAE;kBACvCpC,SAAS,EAAG,+EACVsC,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;kBACHE,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAxC,QAAA,eAE1BlF,OAAA;oBAAKiF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9ClF,OAAA;sBAAKiF,SAAS,EAAG,0FACfsC,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;sBAAArC,QAAA,EACAqC,UAAU,gBAAGvH,OAAA,CAACR,OAAO;wBAACyF,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG+B;oBAAG;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNtF,OAAA;sBAAMiF,SAAS,EAAC,gEAAgE;sBAAAC,QAAA,EAAEoC;oBAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F;gBAAC,GAnBD+B,GAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBK,CAAC;cAEpB,CAAC,CAAC;cAAA;cAEF;cACAtF,OAAA;gBAAKiF,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,gBAC3GlF,OAAA;kBAAKiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3ClF,OAAA;oBAAKiF,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjFlF,OAAA,CAACL,MAAM;sBAACsF,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACNtF,OAAA;oBAAOiF,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEvD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNtF,OAAA;kBACEsH,KAAK,EAAEvG,OAAO,CAACF,oBAAoB,CAAC,IAAI,EAAG;kBAC3C+G,QAAQ,EAAGlB,CAAC,IAAK7B,kBAAkB,CAAC6B,CAAC,CAACC,MAAM,CAACW,KAAK,CAAE;kBACpDO,WAAW,EAAC,0BAA0B;kBACtC5C,SAAS,EAAC,8KAA8K;kBACxL6C,IAAI,EAAC,GAAG;kBACRnC,KAAK,EAAE;oBAAEoC,SAAS,EAAE;kBAAQ;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACFtF,OAAA;kBAAKiF,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAvGDzE,oBAAoB;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwGf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGNtF,OAAA;MAAKiF,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpHlF,OAAA;QAAKiF,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFlF,OAAA;UAAKiF,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/DlF,OAAA;YACEuF,OAAO,EAAEP,YAAa;YACtBgD,QAAQ,EAAEnH,oBAAoB,KAAK,CAAE;YACrCoE,SAAS,EAAG,2HACVpE,oBAAoB,KAAK,CAAC,GACtB,8CAA8C,GAC9C,6DACL,EAAE;YAAAqE,QAAA,gBAEHlF,OAAA,CAACV,WAAW;cAAC2F,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDtF,OAAA;cAAMiF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDtF,OAAA;cAAMiF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAGTtF,OAAA;YAAKiF,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAClGvE,SAAS,CAACwC,GAAG,CAAC,CAAC8E,CAAC,EAAE5E,KAAK,KAAK;cAC3B,MAAM6E,UAAU,GAAGnH,OAAO,CAACsC,KAAK,CAAC,KAAK8E,SAAS,IAAIpH,OAAO,CAACsC,KAAK,CAAC,KAAK,EAAE;cACxE,MAAM+E,SAAS,GAAG/E,KAAK,KAAKxC,oBAAoB;cAChD,oBACEb,OAAA;gBAEEuF,OAAO,EAAEA,CAAA,KAAMzE,uBAAuB,CAACuC,KAAK,CAAE;gBAC9C4B,SAAS,EAAG,sHACVmD,SAAS,GACL,iDAAiD,GACjDF,UAAU,GACV,mDAAmD,GACnD,sEACL,EAAE;gBAAAhD,QAAA,EAEF7B,KAAK,GAAG;cAAC,GAVLA,KAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWJ,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtF,OAAA;YACEuF,OAAO,EAAE1E,oBAAoB,KAAKF,SAAS,CAAC4B,MAAM,GAAG,CAAC,GAAGG,gBAAgB,GAAGqC,QAAS;YACrFE,SAAS,EAAC,kLAAkL;YAAAC,QAAA,gBAE5LlF,OAAA;cAAAkF,QAAA,EACGrE,oBAAoB,KAAKF,SAAS,CAAC4B,MAAM,GAAG,CAAC,gBAC5CvC,OAAA,CAAAE,SAAA;gBAAAgF,QAAA,gBACElF,OAAA;kBAAMiF,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDtF,OAAA;kBAAMiF,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACzC,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACNzE,oBAAoB,KAAKF,SAAS,CAAC4B,MAAM,GAAG,CAAC,gBAC5CvC,OAAA,CAACJ,MAAM;cAACqF,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5CtF,OAAA,CAACT,YAAY;cAAC0F,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CA5eID,QAAQ;EAAA,QACGpB,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAAoJ,EAAA,GAHxBlI,QAAQ;AA8ed,eAAeA,QAAQ;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
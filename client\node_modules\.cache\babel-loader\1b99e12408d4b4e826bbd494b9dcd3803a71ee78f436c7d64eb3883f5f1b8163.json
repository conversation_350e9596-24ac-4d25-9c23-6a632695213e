{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbFlag } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport AuthDebug from '../../../components/AuthDebug';\nimport './responsive.css';\nimport './quiz-improvements.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Check if user is logged in\n  useEffect(() => {\n    if (!user) {\n      message.error('Please login to access quizzes');\n      navigate('/login');\n      return;\n    }\n  }, [user, navigate]);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz data for ID:', id);\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            navigate('/quiz');\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            navigate('/quiz');\n            return;\n          }\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          navigate('/quiz');\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        navigate('/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz - wrapped in useCallback to fix dependency warning\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error('User session expired. Please login again.');\n        navigate('/login');\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: user._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          timeTaken\n        }\n      };\n      console.log('Submitting quiz result:', reportData);\n      await addReport(reportData);\n\n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user._id]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n  if (loading || !user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: !user ? 'Checking authentication...' : 'Loading quiz...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this);\n  }\n  if (!examData || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbX, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"Quiz Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz could not be loaded or has no questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = (currentQuestionIndex + 1) / questions.length * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\",\n    children: [/*#__PURE__*/_jsxDEV(AuthDebug, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900 truncate\",\n              children: examData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progress}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round(progress), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`,\n                children: currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'Multiple Choice' : 'Fill in the Blank'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), answers[currentQuestionIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1 text-white text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this), \"Answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 sm:p-6 flex-1 overflow-y-auto\",\n            style: {\n              maxHeight: 'calc(100vh - 280px)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                children: currentQuestion.name || currentQuestion.question\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this), currentQuestion.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentQuestion.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"w-full max-w-full mx-auto h-auto object-contain rounded-lg\",\n                    style: {\n                      maxHeight: '300px'\n                    },\n                    onError: e => {\n                      console.error('Image failed to load:', currentQuestion.imageUrl);\n                      e.target.style.display = 'none';\n                      const errorDiv = e.target.nextElementSibling;\n                      if (errorDiv) {\n                        errorDiv.style.display = 'flex';\n                      }\n                    },\n                    onLoad: () => {\n                      console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                        className: \"w-12 h-12 mx-auto mb-2 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: \"Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: \"Please check your internet connection\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 sm:space-y-4\",\n              children: (currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ?\n              // Multiple Choice Questions - Enhanced for mobile\n              Object.entries(currentQuestion.options).map(([key, value]) => {\n                const isSelected = answers[currentQuestionIndex] === key;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(key),\n                  className: `w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                  whileHover: {\n                    scale: 1.01\n                  },\n                  whileTap: {\n                    scale: 0.99\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start gap-3 sm:gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 369,\n                        columnNumber: 43\n                      }, this) : key\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 23\n                }, this);\n              }) :\n              /*#__PURE__*/\n              // Enhanced Fill-in-the-Blank Questions with better spacing\n              _jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-sm font-semibold text-gray-700\",\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: answers[currentQuestionIndex] || '',\n                  onChange: e => handleAnswerSelect(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\",\n                  rows: \"4\",\n                  style: {\n                    minHeight: '100px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-xs text-gray-500\",\n                  children: \"Tip: Take your time to write a clear and complete answer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, currentQuestionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestionIndex === 0,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${currentQuestionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\",\n            children: questions.map((_, index) => {\n              const isAnswered = answers[index] !== undefined && answers[index] !== '';\n              const isCurrent = index === currentQuestionIndex;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentQuestionIndex(index),\n                className: `w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${isCurrent ? 'bg-blue-600 text-white scale-110' : isAnswered ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'}`,\n                children: isAnswered && !isCurrent ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 23\n                }, this) : index + 1\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext,\n            className: \"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"Submit Quiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sm:hidden\",\n                  children: \"Submit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this), currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/_jsxDEV(TbFlag, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"lE6uH25rXOZyNMMJpVBaoagyQbY=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useSelector", "message", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbFlag", "getExamById", "addReport", "AuthDebug", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizPlay", "_s", "id", "navigate", "user", "state", "error", "examData", "setExamData", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loading", "setLoading", "loadQuizData", "console", "log", "response", "examId", "success", "data", "length", "duration", "Date", "handleSubmitQuiz", "_id", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "percentage", "round", "reportData", "exam", "result", "wrongAnswers", "totalQuestions", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "answer", "goToNext", "goToPrevious", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "progress", "isTimeWarning", "name", "div", "initial", "width", "animate", "transition", "mode", "opacity", "x", "exit", "type", "answerType", "style", "maxHeight", "imageUrl", "src", "alt", "onError", "e", "target", "display", "errorDiv", "nextElement<PERSON><PERSON>ling", "onLoad", "options", "Object", "entries", "key", "value", "isSelected", "button", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "minHeight", "disabled", "_", "isAnswered", "undefined", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Tb<PERSON><PERSON>, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck, \n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbFlag\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport AuthDebug from '../../../components/AuthDebug';\nimport './responsive.css';\nimport './quiz-improvements.css';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n\n  // Check if user is logged in\n  useEffect(() => {\n    if (!user) {\n      message.error('Please login to access quizzes');\n      navigate('/login');\n      return;\n    }\n  }, [user, navigate]);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz data for ID:', id);\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            navigate('/quiz');\n            return;\n          }\n\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            navigate('/quiz');\n            return;\n          }\n\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          navigate('/quiz');\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        navigate('/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz - wrapped in useCallback to fix dependency warning\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error('User session expired. Please login again.');\n        navigate('/login');\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: user._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          timeTaken\n        }\n      };\n\n      console.log('Submitting quiz result:', reportData);\n      await addReport(reportData);\n\n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user._id]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  if (loading || !user) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">\n            {!user ? 'Checking authentication...' : 'Loading quiz...'}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbX className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-bold text-gray-900 mb-2\">Quiz Not Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz could not be loaded or has no questions.</p>\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-safe\">\n      <AuthDebug />\n      {/* Enhanced Header with better mobile support */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-3 sm:px-4 py-3 sm:py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Info */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-xl font-bold text-gray-900 truncate\">\n                {examData.name}\n              </h1>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Question {currentQuestionIndex + 1} of {questions.length}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${\n              isTimeWarning \n                ? 'bg-red-100 text-red-700 animate-pulse' \n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-5 h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(progress)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Main Content with better mobile support */}\n      <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-6 quiz-container-sm quiz-container-md quiz-container-lg\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden quiz-content-landscape\"\n          >\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                  currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options'\n                    ? 'bg-blue-100 text-blue-800'\n                    : 'bg-green-100 text-green-800'\n                }`}>\n                  {currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' \n                    ? 'Multiple Choice' \n                    : 'Fill in the Blank'}\n                </div>\n                {answers[currentQuestionIndex] && (\n                  <div className=\"flex items-center gap-1 text-white text-sm font-medium\">\n                    <TbCheck className=\"w-4 h-4\" />\n                    Answered\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Question Content - Fixed scrolling and responsive design */}\n            <div className=\"p-4 sm:p-6 flex-1 overflow-y-auto\" style={{ maxHeight: 'calc(100vh - 280px)' }}>\n              <div className=\"mb-6\">\n                <h2 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                  {currentQuestion.name || currentQuestion.question}\n                </h2>\n\n                {/* Enhanced Image Display with better error handling */}\n                {currentQuestion.imageUrl && (\n                  <div className=\"mb-6\">\n                    <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200 p-4\">\n                      <img\n                        src={currentQuestion.imageUrl}\n                        alt=\"Question diagram\"\n                        className=\"w-full max-w-full mx-auto h-auto object-contain rounded-lg\"\n                        style={{ maxHeight: '300px' }}\n                        onError={(e) => {\n                          console.error('Image failed to load:', currentQuestion.imageUrl);\n                          e.target.style.display = 'none';\n                          const errorDiv = e.target.nextElementSibling;\n                          if (errorDiv) {\n                            errorDiv.style.display = 'flex';\n                          }\n                        }}\n                        onLoad={() => {\n                          console.log('Image loaded successfully:', currentQuestion.imageUrl);\n                        }}\n                      />\n                      <div className=\"hidden items-center justify-center h-48 text-gray-500 bg-gray-100 rounded-lg\">\n                        <div className=\"text-center\">\n                          <TbPhoto className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                          <p className=\"text-sm\">Image could not be loaded</p>\n                          <p className=\"text-xs text-gray-400 mt-1\">Please check your internet connection</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Enhanced Answer Options with better responsive design */}\n              <div className=\"space-y-3 sm:space-y-4\">\n                {(currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ? (\n                  // Multiple Choice Questions - Enhanced for mobile\n                  Object.entries(currentQuestion.options).map(([key, value]) => {\n                    const isSelected = answers[currentQuestionIndex] === key;\n                    return (\n                      <motion.button\n                        key={key}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`w-full p-3 sm:p-4 rounded-xl border-2 text-left transition-all duration-200 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 shadow-md'\n                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                        }`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <div className=\"flex items-start gap-3 sm:gap-4\">\n                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold flex-shrink-0 ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" /> : key}\n                          </div>\n                          <span className=\"text-gray-900 font-medium text-sm sm:text-base leading-relaxed\">{value}</span>\n                        </div>\n                      </motion.button>\n                    );\n                  })\n                ) : (\n                  // Enhanced Fill-in-the-Blank Questions with better spacing\n                  <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border-2 border-green-200\">\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                        <TbEdit className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                      </div>\n                      <label className=\"text-sm font-semibold text-gray-700\">\n                        Your Answer:\n                      </label>\n                    </div>\n                    <textarea\n                      value={answers[currentQuestionIndex] || ''}\n                      onChange={(e) => handleAnswerSelect(e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\"\n                      rows=\"4\"\n                      style={{ minHeight: '100px' }}\n                    />\n                    <div className=\"mt-2 text-xs text-gray-500\">\n                      Tip: Take your time to write a clear and complete answer\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Enhanced Navigation Footer - Mobile Responsive */}\n      <div className=\"bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 quiz-navigation-safe\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 py-3 sm:py-4 quiz-navigation-landscape\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Previous Button - Mobile Optimized */}\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all text-sm sm:text-base ${\n                currentQuestionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n              <span className=\"sm:hidden\">Prev</span>\n            </button>\n\n            {/* Question Navigation Dots - Enhanced for mobile */}\n            <div className=\"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-xs sm:max-w-md scrollbar-hide\">\n              {questions.map((_, index) => {\n                const isAnswered = answers[index] !== undefined && answers[index] !== '';\n                const isCurrent = index === currentQuestionIndex;\n                return (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentQuestionIndex(index)}\n                    className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${\n                      isCurrent\n                        ? 'bg-blue-600 text-white scale-110'\n                        : isAnswered\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-200 text-gray-600 hover:bg-gray-300'\n                    }`}\n                  >\n                    {isAnswered && !isCurrent ? (\n                      <TbCheck className=\"w-4 h-4\" />\n                    ) : (\n                      index + 1\n                    )}\n                  </button>\n                );\n              })}\n            </div>\n\n            {/* Next/Submit Button - Mobile Optimized */}\n            <button\n              onClick={currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext}\n              className=\"flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\"\n            >\n              <span>\n                {currentQuestionIndex === questions.length - 1 ? (\n                  <>\n                    <span className=\"hidden sm:inline\">Submit Quiz</span>\n                    <span className=\"sm:hidden\">Submit</span>\n                  </>\n                ) : (\n                  'Next'\n                )}\n              </span>\n              {currentQuestionIndex === questions.length - 1 ? (\n                <TbFlag className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              ) : (\n                <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAO,kBAAkB;AACzB,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAC1B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwB;EAAK,CAAC,GAAGvB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA3B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,IAAI,EAAE;MACTtB,OAAO,CAACwB,KAAK,CAAC,gCAAgC,CAAC;MAC/CH,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;EACF,CAAC,EAAE,CAACC,IAAI,EAAED,QAAQ,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChBE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAErB,EAAE,CAAC;QAE5C,MAAMsB,QAAQ,GAAG,MAAM/B,WAAW,CAAC;UAAEgC,MAAM,EAAEvB;QAAG,CAAC,CAAC;QAClDoB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClB7C,OAAO,CAACwB,KAAK,CAAC,qBAAqB,CAAC;YACpCH,QAAQ,CAAC,OAAO,CAAC;YACjB;UACF;UAEA,IAAI,CAACqB,QAAQ,CAACG,IAAI,CAAClB,SAAS,IAAIe,QAAQ,CAACG,IAAI,CAAClB,SAAS,CAACmB,MAAM,KAAK,CAAC,EAAE;YACpE9C,OAAO,CAACwB,KAAK,CAAC,sCAAsC,CAAC;YACrDH,QAAQ,CAAC,OAAO,CAAC;YACjB;UACF;UAEAK,WAAW,CAACgB,QAAQ,CAACG,IAAI,CAAC;UAC1BjB,YAAY,CAACc,QAAQ,CAACG,IAAI,CAAClB,SAAS,IAAI,EAAE,CAAC;UAC3CO,WAAW,CAAC,CAACQ,QAAQ,CAACG,IAAI,CAACE,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC;UAChDX,YAAY,CAAC,IAAIY,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC,MAAM;UACLR,OAAO,CAAChB,KAAK,CAAC,iBAAiB,EAAEkB,QAAQ,CAAC1C,OAAO,CAAC;UAClDA,OAAO,CAACwB,KAAK,CAACkB,QAAQ,CAAC1C,OAAO,IAAI,qBAAqB,CAAC;UACxDqB,QAAQ,CAAC,OAAO,CAAC;QACnB;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdgB,OAAO,CAAChB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CxB,OAAO,CAACwB,KAAK,CAAC,wCAAwC,CAAC;QACvDH,QAAQ,CAAC,OAAO,CAAC;MACnB,CAAC,SAAS;QACRiB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIlB,EAAE,IAAIE,IAAI,EAAE;MACdiB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACnB,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAM2B,gBAAgB,GAAGrD,WAAW,CAAC,YAAY;IAC/C,IAAI;MACF,IAAI,CAAC0B,IAAI,IAAI,CAACA,IAAI,CAAC4B,GAAG,EAAE;QACtBlD,OAAO,CAACwB,KAAK,CAAC,2CAA2C,CAAC;QAC1DH,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEA,MAAM8B,OAAO,GAAG,IAAIH,IAAI,CAAC,CAAC;MAC1B,MAAMI,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGhB,SAAS,IAAI,IAAI,CAAC;;MAE1D;MACA,IAAIoB,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAG7B,SAAS,CAAC8B,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAG7B,OAAO,CAAC4B,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLG,QAAQ,EAAEA,QAAQ,CAACR,GAAG;UACtBU,UAAU;UACVE,aAAa,EAAEJ,QAAQ,CAACI,aAAa;UACrCD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAME,UAAU,GAAGV,IAAI,CAACW,KAAK,CAAET,cAAc,GAAG5B,SAAS,CAACmB,MAAM,GAAI,GAAG,CAAC;;MAExE;MACA,MAAMmB,UAAU,GAAG;QACjBC,IAAI,EAAE9C,EAAE;QACRE,IAAI,EAAEA,IAAI,CAAC4B,GAAG;QACdiB,MAAM,EAAE;UACNZ,cAAc;UACda,YAAY,EAAEzC,SAAS,CAACmB,MAAM,GAAGS,cAAc;UAC/CQ,UAAU;UACVX;QACF;MACF,CAAC;MAEDZ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwB,UAAU,CAAC;MAClD,MAAMrD,SAAS,CAACqD,UAAU,CAAC;;MAE3B;MACA5C,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;QAC7BG,KAAK,EAAE;UACLwC,UAAU;UACVR,cAAc;UACdc,cAAc,EAAE1C,SAAS,CAACmB,MAAM;UAChCM,SAAS;UACTI;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdxB,OAAO,CAACwB,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC,EAAE,CAACW,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEX,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC4B,GAAG,CAAC,CAAC;;EAE3D;EACAvD,SAAS,CAAC,MAAM;IACd,IAAIsC,QAAQ,IAAI,CAAC,EAAE;MACjBgB,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMqB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BrC,WAAW,CAACsC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACrC,QAAQ,EAAEgB,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAMyB,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGvB,IAAI,CAACC,KAAK,CAACqB,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrCjD,UAAU,CAACwC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAAC3C,oBAAoB,GAAGoD;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIrD,oBAAoB,GAAGF,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;MAC/ChB,uBAAuB,CAAC0C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAItD,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAAC0C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,IAAInC,OAAO,IAAI,CAACf,IAAI,EAAE;IACpB,oBACEP,OAAA;MAAKqE,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGtE,OAAA;QAAKqE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtE,OAAA;UAAKqE,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG1E,OAAA;UAAGqE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACrC,CAAC/D,IAAI,GAAG,4BAA4B,GAAG;QAAiB;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAChE,QAAQ,IAAIE,SAAS,CAACmB,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE/B,OAAA;MAAKqE,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGtE,OAAA;QAAKqE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtE,OAAA,CAACR,GAAG;UAAC6E,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvD1E,OAAA;UAAIqE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E1E,OAAA;UAAGqE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxF1E,OAAA;UACE2E,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,YAAY,CAAE;UACtC+D,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAGhE,SAAS,CAACE,oBAAoB,CAAC;EACvD,MAAM+D,QAAQ,GAAI,CAAC/D,oBAAoB,GAAG,CAAC,IAAIF,SAAS,CAACmB,MAAM,GAAI,GAAG;EACtE,MAAM+C,aAAa,GAAG5D,QAAQ,IAAI,GAAG,CAAC,CAAC;;EAEvC,oBACElB,OAAA;IAAKqE,SAAS,EAAC,+EAA+E;IAAAC,QAAA,gBAC5FtE,OAAA,CAACF,SAAS;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEb1E,OAAA;MAAKqE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EtE,OAAA;QAAKqE,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DtE,OAAA;UAAKqE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhDtE,OAAA;YAAKqE,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBtE,OAAA;cAAIqE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrD5D,QAAQ,CAACqE;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACL1E,OAAA;cAAGqE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,EAACxD,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACmB,MAAM;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN1E,OAAA;YAAKqE,SAAS,EAAG,qFACfS,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAAR,QAAA,gBACDtE,OAAA,CAACZ,OAAO;cAACiF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B1E,OAAA;cAAAsE,QAAA,EAAOX,UAAU,CAACzC,QAAQ;YAAC;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1E,OAAA;UAAKqE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBtE,OAAA;YAAKqE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDtE,OAAA,CAACd,MAAM,CAAC8F,GAAG;cACTX,SAAS,EAAC,+DAA+D;cACzEY,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAEL,QAAS;cAAG,CAAE;cACnCO,UAAU,EAAE;gBAAEpD,QAAQ,EAAE;cAAI;YAAE;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1E,OAAA;YAAKqE,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DtE,OAAA;cAAAsE,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrB1E,OAAA;cAAAsE,QAAA,GAAOhC,IAAI,CAACW,KAAK,CAAC4B,QAAQ,CAAC,EAAC,GAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChHtE,OAAA,CAACb,eAAe;QAACkG,IAAI,EAAC,MAAM;QAAAf,QAAA,eAC1BtE,OAAA,CAACd,MAAM,CAAC8F,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BH,UAAU,EAAE;YAAEpD,QAAQ,EAAE;UAAI,CAAE;UAC9BqC,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAGxGtE,OAAA;YAAKqE,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEtE,OAAA;cAAKqE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtE,OAAA;gBAAKqE,SAAS,EAAG,gDACfO,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,GACtE,2BAA2B,GAC3B,6BACL,EAAE;gBAAApB,QAAA,EACAM,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,GACvE,iBAAiB,GACjB;cAAmB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EACL1D,OAAO,CAACF,oBAAoB,CAAC,iBAC5Bd,OAAA;gBAAKqE,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrEtE,OAAA,CAACT,OAAO;kBAAC8E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1E,OAAA;YAAKqE,SAAS,EAAC,mCAAmC;YAACsB,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAsB,CAAE;YAAAtB,QAAA,gBAC7FtE,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAIqE,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAChFM,eAAe,CAACG,IAAI,IAAIH,eAAe,CAACjC;cAAQ;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,EAGJE,eAAe,CAACiB,QAAQ,iBACvB7F,OAAA;gBAAKqE,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBtE,OAAA;kBAAKqE,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,gBACxFtE,OAAA;oBACE8F,GAAG,EAAElB,eAAe,CAACiB,QAAS;oBAC9BE,GAAG,EAAC,kBAAkB;oBACtB1B,SAAS,EAAC,4DAA4D;oBACtEsB,KAAK,EAAE;sBAAEC,SAAS,EAAE;oBAAQ,CAAE;oBAC9BI,OAAO,EAAGC,CAAC,IAAK;sBACdxE,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEmE,eAAe,CAACiB,QAAQ,CAAC;sBAChEI,CAAC,CAACC,MAAM,CAACP,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/B,MAAMC,QAAQ,GAAGH,CAAC,CAACC,MAAM,CAACG,kBAAkB;sBAC5C,IAAID,QAAQ,EAAE;wBACZA,QAAQ,CAACT,KAAK,CAACQ,OAAO,GAAG,MAAM;sBACjC;oBACF,CAAE;oBACFG,MAAM,EAAEA,CAAA,KAAM;sBACZ7E,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkD,eAAe,CAACiB,QAAQ,CAAC;oBACrE;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF1E,OAAA;oBAAKqE,SAAS,EAAC,8EAA8E;oBAAAC,QAAA,eAC3FtE,OAAA;sBAAKqE,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BtE,OAAA,CAACP,OAAO;wBAAC4E,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5D1E,OAAA;wBAAGqE,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACpD1E,OAAA;wBAAGqE,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAC;sBAAqC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN1E,OAAA;cAAKqE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC,CAACM,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,KAAKd,eAAe,CAAC2B,OAAO;cACtG;cACAC,MAAM,CAACC,OAAO,CAAC7B,eAAe,CAAC2B,OAAO,CAAC,CAAC7D,GAAG,CAAC,CAAC,CAACgE,GAAG,EAAEC,KAAK,CAAC,KAAK;gBAC5D,MAAMC,UAAU,GAAG5F,OAAO,CAACF,oBAAoB,CAAC,KAAK4F,GAAG;gBACxD,oBACE1G,OAAA,CAACd,MAAM,CAAC2H,MAAM;kBAEZlC,OAAO,EAAEA,CAAA,KAAMV,kBAAkB,CAACyC,GAAG,CAAE;kBACvCrC,SAAS,EAAG,+EACVuC,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;kBACHE,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAzC,QAAA,eAE1BtE,OAAA;oBAAKqE,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CtE,OAAA;sBAAKqE,SAAS,EAAG,0FACfuC,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;sBAAAtC,QAAA,EACAsC,UAAU,gBAAG5G,OAAA,CAACT,OAAO;wBAAC8E,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAGgC;oBAAG;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN1E,OAAA;sBAAMqE,SAAS,EAAC,gEAAgE;sBAAAC,QAAA,EAAEqC;oBAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F;gBAAC,GAnBDgC,GAAG;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBK,CAAC;cAEpB,CAAC,CAAC;cAAA;cAEF;cACA1E,OAAA;gBAAKqE,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,gBAC3GtE,OAAA;kBAAKqE,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3CtE,OAAA;oBAAKqE,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjFtE,OAAA,CAACN,MAAM;sBAAC2E,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACN1E,OAAA;oBAAOqE,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEvD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1E,OAAA;kBACE2G,KAAK,EAAE3F,OAAO,CAACF,oBAAoB,CAAC,IAAI,EAAG;kBAC3CmG,QAAQ,EAAGhB,CAAC,IAAKhC,kBAAkB,CAACgC,CAAC,CAACC,MAAM,CAACS,KAAK,CAAE;kBACpDO,WAAW,EAAC,0BAA0B;kBACtC7C,SAAS,EAAC,8KAA8K;kBACxL8C,IAAI,EAAC,GAAG;kBACRxB,KAAK,EAAE;oBAAEyB,SAAS,EAAE;kBAAQ;gBAAE;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACF1E,OAAA;kBAAKqE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA5HD5D,oBAAoB;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6Hf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpHtE,OAAA;QAAKqE,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFtE,OAAA;UAAKqE,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/DtE,OAAA;YACE2E,OAAO,EAAEP,YAAa;YACtBiD,QAAQ,EAAEvG,oBAAoB,KAAK,CAAE;YACrCuD,SAAS,EAAG,2HACVvD,oBAAoB,KAAK,CAAC,GACtB,8CAA8C,GAC9C,6DACL,EAAE;YAAAwD,QAAA,gBAEHtE,OAAA,CAACX,WAAW;cAACgF,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD1E,OAAA;cAAMqE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD1E,OAAA;cAAMqE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAGT1E,OAAA;YAAKqE,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAClG1D,SAAS,CAAC8B,GAAG,CAAC,CAAC4E,CAAC,EAAE1E,KAAK,KAAK;cAC3B,MAAM2E,UAAU,GAAGvG,OAAO,CAAC4B,KAAK,CAAC,KAAK4E,SAAS,IAAIxG,OAAO,CAAC4B,KAAK,CAAC,KAAK,EAAE;cACxE,MAAM6E,SAAS,GAAG7E,KAAK,KAAK9B,oBAAoB;cAChD,oBACEd,OAAA;gBAEE2E,OAAO,EAAEA,CAAA,KAAM5D,uBAAuB,CAAC6B,KAAK,CAAE;gBAC9CyB,SAAS,EAAG,sHACVoD,SAAS,GACL,kCAAkC,GAClCF,UAAU,GACV,yBAAyB,GACzB,6CACL,EAAE;gBAAAjD,QAAA,EAEFiD,UAAU,IAAI,CAACE,SAAS,gBACvBzH,OAAA,CAACT,OAAO;kBAAC8E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAE/B9B,KAAK,GAAG;cACT,GAdIA,KAAK;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeJ,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1E,OAAA;YACE2E,OAAO,EAAE7D,oBAAoB,KAAKF,SAAS,CAACmB,MAAM,GAAG,CAAC,GAAGG,gBAAgB,GAAGiC,QAAS;YACrFE,SAAS,EAAC,kLAAkL;YAAAC,QAAA,gBAE5LtE,OAAA;cAAAsE,QAAA,EACGxD,oBAAoB,KAAKF,SAAS,CAACmB,MAAM,GAAG,CAAC,gBAC5C/B,OAAA,CAAAE,SAAA;gBAAAoE,QAAA,gBACEtE,OAAA;kBAAMqE,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrD1E,OAAA;kBAAMqE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACzC,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACN5D,oBAAoB,KAAKF,SAAS,CAACmB,MAAM,GAAG,CAAC,gBAC5C/B,OAAA,CAACL,MAAM;cAAC0E,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5C1E,OAAA,CAACV,YAAY;cAAC+E,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CAxcID,QAAQ;EAAA,QACGrB,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAA0I,EAAA,GAHxBvH,QAAQ;AA0cd,eAAeA,QAAQ;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
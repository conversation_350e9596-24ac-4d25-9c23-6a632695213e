import React from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { TbDashboard } from "react-icons/tb";
import PageTitle from "../../../components/PageTitle";
import { message, Table } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllReports } from "../../../apicalls/reports";
import { useEffect } from "react";
import moment from "moment";

function AdminReports() {
  const navigate = useNavigate();
  const [reportsData, setReportsData] = React.useState([]);
  const [pagination, setPagination] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0, // total number of records
  });
  const dispatch = useDispatch();
  const [filters, setFilters] = React.useState({
    examName: "",
    userName: "",
  });

  const columns = [
    {
      title: "Exam Name",
      dataIndex: "examName",
      render: (text, record) => String(record.exam?.name || 'N/A'),
    },
    {
      title: "User Name",
      dataIndex: "userName",
      render: (text, record) => String(record.user?.name || 'N/A'),
    },
    {
      title: "Date",
      dataIndex: "date",
      render: (text, record) => (
        <>{moment(record.createdAt).format("DD-MM-YYYY hh:mm:ss")}</>
      ),
    },
    {
      title: "Total Marks",
      dataIndex: "totalQuestions",
      render: (text, record) => String(record.exam?.totalMarks || 0),
    },
    {
      title: "Passing Marks",
      dataIndex: "correctAnswers",
      render: (text, record) => String(record.exam?.passingMarks || 0),
    },
    {
      title: "Obtained Marks",
      dataIndex: "correctAnswers",
      render: (text, record) => {
        const correctAnswers = record.result?.correctAnswers;
        return String(Array.isArray(correctAnswers) ? correctAnswers.length : (correctAnswers || 0));
      },
    },
    {
      title: "Verdict",
      dataIndex: "verdict",
      render: (text, record) => {
        const verdict = record.result?.verdict;
        return typeof verdict === 'string' ? verdict : "N/A";
      },
    },
  ];

  const getData = async (tempFilters, page = 1, limit = 10) => {
    try {
      dispatch(ShowLoading());
      const response = await getAllReports({
        ...tempFilters,
        page,
        limit,
      });
      if (response.success) {
        setReportsData(response.data);
        setPagination({
          ...pagination,
          current: page,
          total: response.pagination.totalReports,
        });
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  useEffect(() => {
    getData(filters, pagination.current, pagination.pageSize);
  }, [filters, pagination.current]);

  const handleTableChange = (pagination) => {
    getData(filters, pagination.current, pagination.pageSize);
  };

  return (
    <div>
      <div className="flex items-center gap-4 mb-4">
        {/* Dashboard Shortcut */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => navigate('/admin/dashboard')}
          className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md"
        >
          <TbDashboard className="w-4 h-4" />
          <span className="hidden sm:inline text-sm font-medium">Dashboard</span>
        </motion.button>

        <PageTitle title="Reports" />
      </div>
      <div className="divider"></div>
      <div className="flex gap-2">
        <input
          type="text"
          placeholder="Exam"
          value={filters.examName}
          onChange={(e) => setFilters({ ...filters, examName: e.target.value })}
        />
        <input
          type="text"
          placeholder="User"
          value={filters.userName}
          onChange={(e) => setFilters({ ...filters, userName: e.target.value })}
        />
        <button
          className="primary-outlined-btn"
          onClick={() => {
            setFilters({
              examName: "",
              userName: "",
            });
            getData({
              examName: "",
              userName: "",
            });
          }}
        >
          Clear
        </button>
        <button
          className="primary-contained-btn"
          onClick={() => getData(filters, 1, pagination.pageSize)}
        >
          Search
        </button>
      </div>
      <Table
  columns={columns}
  dataSource={reportsData}
  className="mt-2"
  pagination={{
    current: pagination.current,
    total: pagination.total,
    showSizeChanger: false, // Disables size changer as per your request
    onChange: (page) => {
      setPagination({
        ...pagination,
        current: page,
      });
      getData(filters, page); // Pass the page, no need to pass pageSize
    },
  }}
/>

    </div>
  );
}

export default AdminReports;

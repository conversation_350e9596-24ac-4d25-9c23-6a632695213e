import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { 
  TbBrain,
  TbTrophy,
  TbClock,
  TbStar,
  TbBook,
  TbChevronRight
} from 'react-icons/tb';
import QuizDashboard from '../../../components/modern/QuizDashboard';
import { getAllQuizzes, getQuizById, submitQuizResult, getUserResults } from '../../../apicalls/quiz';
import Loading from '../../../components/modern/Loading';

const ModernQuizPage = () => {
  const navigate = useNavigate();
  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'quiz', 'result'
  const [quizzes, setQuizzes] = useState([]);
  const [userResults, setUserResults] = useState({});
  const [currentQuiz, setCurrentQuiz] = useState(null);
  const [currentQuestions, setCurrentQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [quizLoading, setQuizLoading] = useState(false);

  // Load initial data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Show skeleton immediately, don't wait for API
      setLoading(false);

      // Check cache first
      const cachedQuizzes = localStorage.getItem('quizzes_cache');
      const cachedResults = localStorage.getItem('quiz_results_cache');
      const cacheTime = localStorage.getItem('quiz_cache_time');
      const now = Date.now();

      // Use cache if less than 5 minutes old
      if (cachedQuizzes && cachedResults && cacheTime && (now - parseInt(cacheTime)) < 300000) {
        setQuizzes(JSON.parse(cachedQuizzes));
        setUserResults(JSON.parse(cachedResults));
        return;
      }

      // Load quizzes and user results in parallel
      const [quizzesResponse, resultsResponse] = await Promise.all([
        getAllQuizzes(),
        getUserResults()
      ]);

      if (quizzesResponse.success) {
        setQuizzes(quizzesResponse.data || []);
        // Cache the data
        localStorage.setItem('quizzes_cache', JSON.stringify(quizzesResponse.data || []));
        localStorage.setItem('quiz_cache_time', Date.now().toString());
      } else {
        // Fallback to demo data if API fails
        setQuizzes([
          {
            _id: 'demo1',
            name: 'Mathematics Quiz - Algebra Basics',
            subject: 'Mathematics',
            duration: 30,
            questions: Array(15).fill({}),
            xpPoints: 150,
            passingMarks: 60,
            class: '7',
            category: 'Practice Test'
          },
          {
            _id: 'demo2',
            name: 'Science Quiz - Physics Fundamentals',
            subject: 'Physics',
            duration: 45,
            questions: Array(20).fill({}),
            xpPoints: 200,
            passingMarks: 70,
            class: '8',
            category: 'Chapter Test'
          },
          {
            _id: 'demo3',
            name: 'English Grammar and Comprehension',
            subject: 'English',
            duration: 25,
            questions: Array(12).fill({}),
            xpPoints: 120,
            passingMarks: 60,
            class: '7',
            category: 'Weekly Test'
          },
          {
            _id: 'demo4',
            name: 'Chemistry - Periodic Table',
            subject: 'Chemistry',
            duration: 35,
            questions: Array(18).fill({}),
            xpPoints: 180,
            passingMarks: 65,
            class: '9',
            category: 'Unit Test'
          },
          {
            _id: 'demo5',
            name: 'History - World War II',
            subject: 'History',
            duration: 40,
            questions: Array(16).fill({}),
            xpPoints: 160,
            passingMarks: 60,
            class: '10',
            category: 'Chapter Test'
          }
        ]);
      }

      if (resultsResponse.success) {
        // Convert results array to object with quiz ID as key
        const resultsMap = {};
        (resultsResponse.data || []).forEach(result => {
          resultsMap[result.quiz] = result;
        });
        setUserResults(resultsMap);
      } else {
        // Demo results
        setUserResults({
          'demo1': {
            percentage: 85,
            correctAnswers: 13,
            totalQuestions: 15,
            xpEarned: 150,
            completedAt: new Date().toISOString()
          },
          'demo2': {
            percentage: 45,
            correctAnswers: 9,
            totalQuestions: 20,
            xpEarned: 0,
            completedAt: new Date(Date.now() - 86400000).toISOString() // Yesterday
          }
        });
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Set demo data on error
      setQuizzes([
        {
          _id: 'demo1',
          name: 'Mathematics Quiz - Algebra Basics',
          subject: 'Mathematics',
          duration: 30,
          questions: Array(15).fill({}),
          xpPoints: 150,
          passingMarks: 60,
          class: '7',
          category: 'Practice Test'
        },
        {
          _id: 'demo2',
          name: 'Science Quiz - Physics Fundamentals',
          subject: 'Physics',
          duration: 45,
          questions: Array(20).fill({}),
          xpPoints: 200,
          passingMarks: 70,
          class: '8',
          category: 'Chapter Test'
        },
        {
          _id: 'demo3',
          name: 'English Grammar Test',
          subject: 'English',
          duration: 25,
          questions: Array(12).fill({}),
          xpPoints: 120,
          passingMarks: 60,
          class: '7',
          category: 'Weekly Test'
        }
      ]);
      setUserResults({
        'demo1': {
          percentage: 85,
          correctAnswers: 13,
          totalQuestions: 15,
          xpEarned: 150,
          completedAt: new Date().toISOString()
        },
        'demo3': {
          percentage: 75,
          correctAnswers: 9,
          totalQuestions: 12,
          xpEarned: 120,
          completedAt: new Date(Date.now() - 172800000).toISOString() // 2 days ago
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle quiz start
  const handleQuizStart = async (quiz) => {
    try {
      setQuizLoading(true);

      // Get full quiz details with questions
      const response = await getQuizById(quiz._id);

      if (response.success && response.data) {
        setCurrentQuiz(response.data);
        setCurrentQuestions(response.data.questions || []);
        setCurrentView('quiz');
      } else {
        // Demo questions for testing
        const demoQuestions = [
          {
            _id: 'q1',
            name: 'What is the value of x in the equation 2x + 5 = 15?',
            type: 'Options',
            answerType: 'Options',
            questionType: 'Options',
            options: {
              A: 'x = 5',
              B: 'x = 10',
              C: 'x = 7.5',
              D: 'x = 2.5'
            },
            correctAnswer: 'A',
            correctOption: 'A'
          },
          {
            _id: 'q2',
            name: 'Solve for y: 3y - 7 = 14',
            type: 'Options',
            answerType: 'Options',
            questionType: 'Options',
            options: {
              A: 'y = 7',
              B: 'y = 21',
              C: 'y = 5',
              D: 'y = 3'
            },
            correctAnswer: 'A',
            correctOption: 'A'
          },
          {
            _id: 'q3',
            name: 'What is the chemical symbol for Gold?',
            type: 'Fill in the Blank',
            answerType: 'Fill in the Blank',
            questionType: 'Fill in the Blank',
            options: {},
            correctAnswer: 'Au',
            inputType: 'short' // For short answers
          },
          {
            _id: 'q4',
            name: 'Which planet is known as the Red Planet?',
            type: 'Options',
            answerType: 'Options',
            questionType: 'Options',
            options: {
              A: 'Mars',
              B: 'Venus',
              C: 'Jupiter',
              D: 'Saturn'
            },
            correctAnswer: 'A',
            correctOption: 'A'
          },
          {
            _id: 'q5',
            name: 'Explain the process of photosynthesis in plants.',
            type: 'Fill in the Blank',
            answerType: 'Fill in the Blank',
            questionType: 'Fill in the Blank',
            options: {},
            correctAnswer: 'Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen.',
            inputType: 'long' // For longer answers
          },
          {
            _id: 'q6',
            name: 'What geometric shape is shown in the image below?',
            type: 'picture_based',
            answerType: 'Options',
            questionType: 'picture_based',
            imageUrl: 'https://picsum.photos/300/200?random=1',
            image: 'https://picsum.photos/300/200?random=1',
            options: {
              A: 'Triangle',
              B: 'Square',
              C: 'Circle',
              D: 'Rectangle'
            },
            correctAnswer: 'A',
            correctOption: 'A'
          }
        ];

        // Validate demo questions before setting
        const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);
        console.log('Setting demo questions:', validatedQuestions);

        setCurrentQuiz(quiz);
        setCurrentQuestions(validatedQuestions);
        setCurrentView('quiz');
      }
    } catch (error) {
      console.error('Error starting quiz:', error);
      // Demo questions on error
      const demoQuestions = [
        {
          _id: 'q1',
          name: 'What is the value of x in the equation 2x + 5 = 15?',
          type: 'Options',
          answerType: 'Options',
          questionType: 'Options',
          options: {
            A: 'x = 5',
            B: 'x = 10',
            C: 'x = 7.5',
            D: 'x = 2.5'
          },
          correctAnswer: 'A',
          correctOption: 'A'
        },
        {
          _id: 'q2',
          name: 'What is the capital of France?',
          type: 'Fill in the Blank',
          answerType: 'Fill in the Blank',
          questionType: 'Fill in the Blank',
          options: {},
          correctAnswer: 'Paris'
        }
      ];

      // Validate demo questions before setting
      const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);
      console.log('Setting error demo questions:', validatedQuestions);

      setCurrentQuiz(quiz);
      setCurrentQuestions(validatedQuestions);
      setCurrentView('quiz');
    } finally {
      setQuizLoading(false);
    }
  };

  // Handle quiz submission
  const handleQuizSubmit = async (answers) => {
    try {
      setQuizLoading(true);
      
      // Prepare submission data
      const submissionData = {
        quizId: currentQuiz._id,
        answers: Object.entries(answers).map(([questionId, answer]) => ({
          questionId,
          answer
        }))
      };

      const response = await submitQuizResult(submissionData);
      
      if (response.success) {
        message.success('Quiz submitted successfully!');
        
        // Refresh dashboard data to show updated results
        await loadDashboardData();
        
        // Navigate to results page
        navigate(`/quiz/${currentQuiz._id}/result`);
      } else {
        message.error(response.message || 'Failed to submit quiz');
      }
    } catch (error) {
      console.error('Error submitting quiz:', error);
      message.error('Failed to submit quiz');
    } finally {
      setQuizLoading(false);
    }
  };

  // Handle back to dashboard
  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setCurrentQuiz(null);
    setCurrentQuestions([]);
  };

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loading size="lg" />
          <p className="mt-4 text-gray-600">Loading your quizzes...</p>
        </div>
      </div>
    );
  }

  // Render quiz interface
  if (currentView === 'quiz' && currentQuiz) {
    return (
      <div className="relative">
        {quizLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 text-center">
              <Loading size="lg" />
              <p className="mt-4 text-gray-600">Processing your quiz...</p>
            </div>
          </div>
        )}
        
        {/* Redirect to new QuizPlay component */}
        {Array.isArray(currentQuestions) && currentQuestions.length > 0 ? (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">🚀</div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">Redirecting to Quiz...</h2>
              <p className="text-gray-600 mb-4">Taking you to the new quiz interface.</p>
              <button
                onClick={() => navigate(`/quiz/${currentQuiz._id}/play`)}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start Quiz
              </button>
            </div>
          </div>
        ) : (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">📝</div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">No Questions Available</h2>
              <p className="text-gray-600 mb-4">This quiz doesn't have any questions yet.</p>
              <button
                onClick={handleBackToDashboard}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Render dashboard
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex items-center justify-center mb-6">
              <TbBrain className="w-16 h-16 text-blue-200 mr-4" />
              <h1 className="text-4xl lg:text-6xl font-bold">
                Brain<span className="text-blue-200">Wave</span>
              </h1>
            </div>
            <p className="text-xl lg:text-2xl text-blue-100 mb-8">
              Challenge your brain, Beat the rest
            </p>
            <div className="flex items-center justify-center gap-8 text-blue-100">
              <div className="flex items-center gap-2">
                <TbTrophy className="w-6 h-6" />
                <span>Track Progress</span>
              </div>
              <div className="flex items-center gap-2">
                <TbStar className="w-6 h-6" />
                <span>Earn XP</span>
              </div>
              <div className="flex items-center gap-2">
                <TbBook className="w-6 h-6" />
                <span>Learn & Grow</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Dashboard */}
      <QuizDashboard
        quizzes={quizzes}
        userResults={userResults}
        onQuizStart={handleQuizStart}
        loading={loading}
      />

      {/* Quick Stats Footer */}
      <div className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="p-6 bg-blue-50 rounded-xl"
            >
              <TbBrain className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Learning</h3>
              <p className="text-gray-600">AI-powered questions adapted to your learning pace</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="p-6 bg-green-50 rounded-xl"
            >
              <TbTrophy className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Track Progress</h3>
              <p className="text-gray-600">Monitor your improvement with detailed analytics</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="p-6 bg-purple-50 rounded-xl"
            >
              <TbStar className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Earn Rewards</h3>
              <p className="text-gray-600">Collect XP points and unlock achievements</p>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernQuizPage;

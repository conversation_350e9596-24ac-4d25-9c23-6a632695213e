{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: typeof question.correctAnswer === 'string' ? question.correctAnswer : String(question.correctAnswer || ''),\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail';\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken,\n          // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n      console.log('📋 Report data being submitted:', reportData);\n      try {\n        const response = await addReport(reportData);\n        console.log('📊 Quiz submission response:', response);\n        if (response.success) {\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null,\n            // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category\n          };\n          console.log('🎯 Navigating to results with data:', navigationState);\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          message.error(response.message || 'Failed to submit quiz');\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        message.error('Network error while submitting quiz');\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n    // Debug logging\n    console.log('Current question:', {\n      type: currentQ.type,\n      answerType: currentQ.answerType,\n      options: currentQ.options,\n      optionsType: typeof currentQ.options,\n      optionsLength: Array.isArray(currentQ.options) ? currentQ.options.length : 'Not array'\n    });\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-800 font-medium\",\n            children: \"No options found for this question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              className: \"text-sm text-yellow-600 cursor-pointer\",\n              children: \"Show question data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\",\n              children: JSON.stringify(currentQ, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: ['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index);\n            const isSelected = answers[currentQuestion] === option;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAnswerSelect(option),\n              className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg leading-relaxed flex-1 text-left\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: options.map((option, index) => {\n        const optionLetter = String.fromCharCode(65 + index);\n        const isSelected = answers[currentQuestion] === option;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleAnswerSelect(option),\n          className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n              children: optionLetter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg leading-relaxed flex-1 text-left\",\n              children: typeof option === 'string' ? option : JSON.stringify(option)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-800 text-sm font-medium mb-2\",\n          children: \"Fill in the blank:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700\",\n          children: \"Type your answer in the box below\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: answers[currentQuestion] || '',\n          onChange: e => handleAnswerSelect(e.target.value),\n          placeholder: \"Type your answer here...\",\n          className: \"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this);\n  }\n  if (!quiz || !questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Question Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Unable to load the current question.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Invalid Question Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"The question data is corrupted or invalid.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => startTransition(() => navigate('/quiz')),\n              className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-6 h-6 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: quiz.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Question \", currentQuestion + 1, \" of \", questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 px-4 py-2 rounded-lg ${timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${(currentQuestion + 1) / questions.length * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-4\",\n            children: typeof currentQ.name === 'string' ? currentQ.name : 'Question'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), currentQ.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 bg-gray-50 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQ.image,\n              alt: \"Question diagram\",\n              className: \"max-w-full h-auto rounded-lg shadow-lg mx-auto block\",\n              style: {\n                maxHeight: '400px'\n              },\n              onError: e => {\n                e.target.style.display = 'none';\n                // Show fallback message\n                const fallback = document.createElement('div');\n                fallback.className = 'text-center py-8 text-gray-500';\n                fallback.innerHTML = '<p>Could not load diagram</p>';\n                e.target.parentNode.appendChild(fallback);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 mb-8\",\n          children: renderAnswerSection()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestion === 0,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${currentQuestion === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSubmitQuiz,\n            className: \"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this), \"Submit Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 483,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"fY1y2RfP35t8yXNHY5IyCXmOpUg=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "message", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "id", "navigate", "user", "state", "loading", "setLoading", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "console", "log", "_id", "token", "localStorage", "getItem", "error", "response", "examId", "success", "data", "length", "Array", "fill", "duration", "Date", "handleSubmitQuiz", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "questionId", "questionName", "name", "String", "percentage", "round", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "timeSpent", "points", "totalQuestions", "navigationState", "xpData", "quizName", "quizSubject", "subject", "category", "apiError", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "renderAnswerSection", "questionType", "currentQ", "type", "answerType", "options", "optionsType", "optionsLength", "isArray", "toLowerCase", "renderMultipleChoice", "renderFillInTheBlank", "renderImageQuestion", "Object", "values", "option1", "option2", "option3", "option4", "filter", "Boolean", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stringify", "option", "optionLetter", "fromCharCode", "isSelected", "onClick", "value", "onChange", "e", "target", "placeholder", "autoFocus", "isLastQuestion", "style", "width", "image", "src", "alt", "maxHeight", "onError", "display", "fallback", "document", "createElement", "innerHTML", "parentNode", "append<PERSON><PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { \n  Tb<PERSON><PERSON>, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        \n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: typeof question.correctAnswer === 'string' ? question.correctAnswer : String(question.correctAnswer || ''),\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken, // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n\n      console.log('📋 Report data being submitted:', reportData);\n\n      try {\n        const response = await addReport(reportData);\n        console.log('📊 Quiz submission response:', response);\n\n        if (response.success) {\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null, // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category\n          };\n\n          console.log('🎯 Navigating to results with data:', navigationState);\n\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          message.error(response.message || 'Failed to submit quiz');\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        message.error('Network error while submitting quiz');\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n    // Debug logging\n    console.log('Current question:', {\n      type: currentQ.type,\n      answerType: currentQ.answerType,\n      options: currentQ.options,\n      optionsType: typeof currentQ.options,\n      optionsLength: Array.isArray(currentQ.options) ? currentQ.options.length : 'Not array'\n    });\n\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return (\n        <div className=\"space-y-4\">\n          <div className=\"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <p className=\"text-yellow-800 font-medium\">No options found for this question</p>\n            <details className=\"mt-2\">\n              <summary className=\"text-sm text-yellow-600 cursor-pointer\">Show question data</summary>\n              <pre className=\"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\">\n                {JSON.stringify(currentQ, null, 2)}\n              </pre>\n            </details>\n          </div>\n\n          {/* Fallback test options */}\n          <div className=\"space-y-3\">\n            {['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion] === option;\n\n              return (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                >\n                  <div className=\"flex items-start gap-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg leading-relaxed flex-1 text-left\">\n                      {option}\n                    </span>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index);\n          const isSelected = answers[currentQuestion] === option;\n\n          return (\n            <button\n              key={index}\n              onClick={() => handleAnswerSelect(option)}\n              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {optionLetter}\n                </div>\n                <span className=\"text-lg leading-relaxed flex-1 text-left\">\n                  {typeof option === 'string' ? option : JSON.stringify(option)}\n                </span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <p className=\"text-blue-800 text-sm font-medium mb-2\">Fill in the blank:</p>\n          <p className=\"text-gray-700\">Type your answer in the box below</p>\n        </div>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={answers[currentQuestion] || ''}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\"\n            autoFocus\n          />\n        </div>\n      </div>\n    );\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Question Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">Unable to load the current question.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Invalid Question Data</h2>\n            <p className=\"text-gray-600 mb-6\">The question data is corrupted or invalid.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <button\n                onClick={() => startTransition(() => navigate('/quiz'))}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <TbArrowLeft className=\"w-6 h-6 text-gray-600\" />\n              </button>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{quiz.name}</h1>\n                <p className=\"text-sm text-gray-600\">\n                  Question {currentQuestion + 1} of {questions.length}\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${\n                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}>\n                <TbClock className=\"w-5 h-5\" />\n                <span className=\"font-semibold\">{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n          </div>\n          \n          {/* Progress bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto p-6\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\">\n          {/* Question */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}\n            </h2>\n            \n            {currentQ.image && (\n              <div className=\"mb-6 bg-gray-50 rounded-lg p-4\">\n                <img\n                  src={currentQ.image}\n                  alt=\"Question diagram\"\n                  className=\"max-w-full h-auto rounded-lg shadow-lg mx-auto block\"\n                  style={{ maxHeight: '400px' }}\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    // Show fallback message\n                    const fallback = document.createElement('div');\n                    fallback.className = 'text-center py-8 text-gray-500';\n                    fallback.innerHTML = '<p>Could not load diagram</p>';\n                    e.target.parentNode.appendChild(fallback);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Section - Different types based on question type */}\n          <div className=\"space-y-4 mb-8\">\n            {renderAnswerSection()}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex justify-between items-center\">\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                className=\"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\"\n              >\n                <TbCheck className=\"w-5 h-5\" />\n                Submit Quiz\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbArrowRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAK,CAAC,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFb,UAAU,CAAC,IAAI,CAAC;QAChBc,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpB,EAAE,CAAC;QAExC,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACmB,GAAG,EAAE;UACtB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAI,CAACF,KAAK,EAAE;YACVH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnD/B,OAAO,CAACoC,KAAK,CAAC,gCAAgC,CAAC;YAC/CxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;QAEA,MAAMyB,QAAQ,GAAG,MAAMhC,WAAW,CAAC;UAAEiC,MAAM,EAAE3B;QAAG,CAAC,CAAC;QAClDmB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClBxC,OAAO,CAACoC,KAAK,CAAC,qBAAqB,CAAC;YACpCxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAACyB,QAAQ,CAACG,IAAI,CAACrB,SAAS,IAAIkB,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAACsB,MAAM,KAAK,CAAC,EAAE;YACpEzC,OAAO,CAACoC,KAAK,CAAC,sCAAsC,CAAC;YACrDxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAM,OAAO,CAACmB,QAAQ,CAACG,IAAI,CAAC;UACtBpB,YAAY,CAACiB,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAAC;UACrCK,UAAU,CAAC,IAAIkB,KAAK,CAACL,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAACsB,MAAM,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;UAC9DjB,WAAW,CAACW,QAAQ,CAACG,IAAI,CAACI,QAAQ,GAAG,EAAE,CAAC;UACxChB,YAAY,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;UACxBf,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEM,QAAQ,CAACG,IAAI,CAAC;QACzD,CAAC,MAAM;UACLV,OAAO,CAACM,KAAK,CAAC,iBAAiB,EAAEC,QAAQ,CAACrC,OAAO,CAAC;UAClDA,OAAO,CAACoC,KAAK,CAACC,QAAQ,CAACrC,OAAO,IAAI,qBAAqB,CAAC;UACxDJ,eAAe,CAAC,MAAM;YACpBgB,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CpC,OAAO,CAACoC,KAAK,CAAC,wCAAwC,CAAC;QACvDxC,eAAe,CAAC,MAAM;UACpBgB,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIL,EAAE,IAAIE,IAAI,EAAE;MACdgB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAClB,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMiC,gBAAgB,GAAGnD,WAAW,CAAC,YAAY;IAC/C,IAAI;MAEF,IAAIoD,WAAW,GAAGlC,IAAI;MACtB,IAAI,CAACkC,WAAW,IAAI,CAACA,WAAW,CAACf,GAAG,EAAE;QACpC,MAAMgB,UAAU,GAAGd,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIa,UAAU,EAAE;UACd,IAAI;YACFD,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOZ,KAAK,EAAE;YACdN,OAAO,CAACM,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvDxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACmC,WAAW,IAAI,CAACA,WAAW,CAACf,GAAG,EAAE;QACpChC,OAAO,CAACoC,KAAK,CAAC,2CAA2C,CAAC;QAC1DxC,eAAe,CAAC,MAAM;UACpBgB,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAMuC,OAAO,GAAG,IAAIN,IAAI,CAAC,CAAC;MAC1B,MAAMO,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGxB,SAAS,IAAI,IAAI,CAAC;MAE1D,IAAI4B,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGrC,SAAS,CAACsC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGrC,OAAO,CAACoC,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLQ,UAAU,EAAEL,QAAQ,CAAC1B,GAAG,IAAK,YAAW2B,KAAM,EAAC;UAC/CK,YAAY,EAAE,OAAON,QAAQ,CAACO,IAAI,KAAK,QAAQ,GAAGP,QAAQ,CAACO,IAAI,GAAI,YAAWN,KAAK,GAAG,CAAE,EAAC;UACzFC,UAAU,EAAE,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGM,MAAM,CAACN,UAAU,IAAI,EAAE,CAAC;UAClFE,aAAa,EAAE,OAAOJ,QAAQ,CAACI,aAAa,KAAK,QAAQ,GAAGJ,QAAQ,CAACI,aAAa,GAAGI,MAAM,CAACR,QAAQ,CAACI,aAAa,IAAI,EAAE,CAAC;UACzHD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMM,UAAU,GAAGd,IAAI,CAACe,KAAK,CAAEb,cAAc,GAAGpC,SAAS,CAACsB,MAAM,GAAI,GAAG,CAAC;MACxE,MAAM4B,OAAO,GAAGF,UAAU,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM;MAElD,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAE5D,EAAE;QACRE,IAAI,EAAEkC,WAAW,CAACf,GAAG;QACrBwC,MAAM,EAAE;UACNjB,cAAc;UACdkB,YAAY,EAAEtD,SAAS,CAACsB,MAAM,GAAGc,cAAc;UAC/CY,UAAU;UACVO,KAAK,EAAEP,UAAU;UACjBE,OAAO,EAAEA,OAAO;UAChBjB,SAAS;UACTuB,SAAS,EAAEvB,SAAS;UAAE;UACtBwB,MAAM,EAAErB,cAAc,GAAG,EAAE;UAC3BsB,cAAc,EAAE1D,SAAS,CAACsB;QAC5B;MACF,CAAC;MAEDX,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEuC,UAAU,CAAC;MAE1D,IAAI;QACF,MAAMjC,QAAQ,GAAG,MAAM/B,SAAS,CAACgE,UAAU,CAAC;QAC5CxC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEM,QAAQ,CAAC;QAErD,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMuC,eAAe,GAAG;YACtBX,UAAU;YACVZ,cAAc;YACdsB,cAAc,EAAE1D,SAAS,CAACsB,MAAM;YAChCW,SAAS;YACTI,aAAa;YACbuB,MAAM,EAAE1C,QAAQ,CAAC0C,MAAM,IAAI,IAAI;YAAE;YACjCC,QAAQ,EAAE/D,IAAI,CAACgD,IAAI;YACnBgB,WAAW,EAAEhE,IAAI,CAACiE,OAAO,IAAIjE,IAAI,CAACkE;UACpC,CAAC;UAEDrD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE+C,eAAe,CAAC;UAEnElF,eAAe,CAAC,MAAM;YACpBgB,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;cAC7BG,KAAK,EAAEgE;YACT,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLhD,OAAO,CAACM,KAAK,CAAC,2BAA2B,EAAEC,QAAQ,CAACrC,OAAO,CAAC;UAC5DA,OAAO,CAACoC,KAAK,CAACC,QAAQ,CAACrC,OAAO,IAAI,uBAAuB,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOoF,QAAQ,EAAE;QACjBtD,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEgD,QAAQ,CAAC;QACzDpF,OAAO,CAACoC,KAAK,CAAC,qCAAqC,CAAC;MACtD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CpC,OAAO,CAACoC,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC,EAAE,CAACT,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEZ,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEvD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAI+B,QAAQ,IAAI,CAAC,EAAE;MACjB;MACA;IACF;IAEA,MAAM4D,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B5D,WAAW,CAAC6D,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAAC5D,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgE,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC,GAAGpE,OAAO,CAAC;IAC/BoE,UAAU,CAACtE,eAAe,CAAC,GAAGqE,MAAM;IACpClE,UAAU,CAACmE,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIvE,eAAe,GAAGF,SAAS,CAACsB,MAAM,GAAG,CAAC,EAAE;MAC1CnB,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMwE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIxE,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMyE,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAG3C,IAAI,CAACC,KAAK,CAACyC,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,YAAY,GAAGC,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACE,UAAU,IAAI,KAAK;;IAElE;IACA1E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BwE,IAAI,EAAED,QAAQ,CAACC,IAAI;MACnBC,UAAU,EAAEF,QAAQ,CAACE,UAAU;MAC/BC,OAAO,EAAEH,QAAQ,CAACG,OAAO;MACzBC,WAAW,EAAE,OAAOJ,QAAQ,CAACG,OAAO;MACpCE,aAAa,EAAEjE,KAAK,CAACkE,OAAO,CAACN,QAAQ,CAACG,OAAO,CAAC,GAAGH,QAAQ,CAACG,OAAO,CAAChE,MAAM,GAAG;IAC7E,CAAC,CAAC;IAEF,QAAQ4D,YAAY,CAACQ,WAAW,CAAC,CAAC;MAChC,KAAK,KAAK;MACV,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;QACnB,OAAOC,oBAAoB,CAAC,CAAC;MAE/B,KAAK,MAAM;MACX,KAAK,mBAAmB;MACxB,KAAK,WAAW;MAChB,KAAK,MAAM;QACT,OAAOC,oBAAoB,CAAC,CAAC;MAE/B,KAAK,OAAO;MACZ,KAAK,SAAS;QACZ,OAAOC,mBAAmB,CAAC,CAAC;MAE9B;QACE;QACA,OAAOF,oBAAoB,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIL,OAAO,GAAG,EAAE;;IAEhB;IACA,IAAI/D,KAAK,CAACkE,OAAO,CAACN,QAAQ,CAACG,OAAO,CAAC,EAAE;MACnCA,OAAO,GAAGH,QAAQ,CAACG,OAAO;IAC5B,CAAC,MAAM,IAAIH,QAAQ,CAACG,OAAO,IAAI,OAAOH,QAAQ,CAACG,OAAO,KAAK,QAAQ,EAAE;MACnE;MACAA,OAAO,GAAGQ,MAAM,CAACC,MAAM,CAACZ,QAAQ,CAACG,OAAO,CAAC;IAC3C,CAAC,MAAM,IAAIH,QAAQ,CAACa,OAAO,IAAIb,QAAQ,CAACc,OAAO,EAAE;MAC/C;MACAX,OAAO,GAAG,CAACH,QAAQ,CAACa,OAAO,EAAEb,QAAQ,CAACc,OAAO,EAAEd,QAAQ,CAACe,OAAO,EAAEf,QAAQ,CAACgB,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IACpG;IAEA,IAAI,CAACf,OAAO,IAAIA,OAAO,CAAChE,MAAM,KAAK,CAAC,EAAE;MACpC;MACA,oBACEjC,OAAA;QAAKiH,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBlH,OAAA;UAAKiH,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChFlH,OAAA;YAAGiH,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjFtH,OAAA;YAASiH,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACvBlH,OAAA;cAASiH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACxFtH,OAAA;cAAKiH,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACrFzE,IAAI,CAAC8E,SAAS,CAACzB,QAAQ,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNtH,OAAA;UAAKiH,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACjE,GAAG,CAAC,CAACuE,MAAM,EAAErE,KAAK,KAAK;YACnG,MAAMsE,YAAY,GAAG/D,MAAM,CAACgE,YAAY,CAAC,EAAE,GAAGvE,KAAK,CAAC;YACpD,MAAMwE,UAAU,GAAG5G,OAAO,CAACF,eAAe,CAAC,KAAK2G,MAAM;YAEtD,oBACExH,OAAA;cAEE4H,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAACuC,MAAM,CAAE;cAC1CP,SAAS,EAAG,wFACVU,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;cAAAT,QAAA,eAEHlH,OAAA;gBAAKiH,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrClH,OAAA;kBAAKiH,SAAS,EAAG,2FACfU,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;kBAAAT,QAAA,EACAO;gBAAY;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNtH,OAAA;kBAAMiH,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EACvDM;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GAnBDnE,KAAK;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBJ,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,oBACEtH,OAAA;MAAKiH,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBjB,OAAO,CAAChD,GAAG,CAAC,CAACuE,MAAM,EAAErE,KAAK,KAAK;QAC9B,MAAMsE,YAAY,GAAG/D,MAAM,CAACgE,YAAY,CAAC,EAAE,GAAGvE,KAAK,CAAC;QACpD,MAAMwE,UAAU,GAAG5G,OAAO,CAACF,eAAe,CAAC,KAAK2G,MAAM;QAEtD,oBACExH,OAAA;UAEE4H,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAACuC,MAAM,CAAE;UAC1CP,SAAS,EAAG,wFACVU,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;UAAAT,QAAA,eAEHlH,OAAA;YAAKiH,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrClH,OAAA;cAAKiH,SAAS,EAAG,2FACfU,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;cAAAT,QAAA,EACAO;YAAY;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtH,OAAA;cAAMiH,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACvD,OAAOM,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG/E,IAAI,CAAC8E,SAAS,CAACC,MAAM;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC,GAnBDnE,KAAK;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBJ,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMf,oBAAoB,GAAGA,CAAA,KAAM;IACjC,oBACEvG,OAAA;MAAKiH,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlH,OAAA;QAAKiH,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DlH,OAAA;UAAGiH,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5EtH,OAAA;UAAGiH,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACNtH,OAAA;QAAKiH,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBlH,OAAA;UACE+F,IAAI,EAAC,MAAM;UACX8B,KAAK,EAAE9G,OAAO,CAACF,eAAe,CAAC,IAAI,EAAG;UACtCiH,QAAQ,EAAGC,CAAC,IAAK9C,kBAAkB,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC,0BAA0B;UACtChB,SAAS,EAAC,mHAAmH;UAC7HiB,SAAS;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMd,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIV,QAAQ,CAACG,OAAO,IAAI/D,KAAK,CAACkE,OAAO,CAACN,QAAQ,CAACG,OAAO,CAAC,IAAIH,QAAQ,CAACG,OAAO,CAAChE,MAAM,GAAG,CAAC,EAAE;MACtF,OAAOqE,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAOC,oBAAoB,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,IAAIhG,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKiH,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlH,OAAA;QAAKiH,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlH,OAAA;UAAKiH,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGtH,OAAA;UAAGiH,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC7G,IAAI,IAAI,CAACE,SAAS,CAACsB,MAAM,EAAE;IAC9B,oBACEjC,OAAA;MAAKiH,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlH,OAAA;QAAKiH,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFlH,OAAA;UAAKiH,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlH,OAAA;YAAIiH,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFtH,OAAA;YAAGiH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/EtH,OAAA;YACE4H,OAAO,EAAEA,CAAA,KAAMxI,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD6G,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC3G,SAAS,CAACE,eAAe,CAAC,EAAE;IAC/B,oBACEb,OAAA;MAAKiH,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlH,OAAA;QAAKiH,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFlH,OAAA;UAAKiH,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlH,OAAA;YAAIiH,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EtH,OAAA;YAAGiH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1EtH,OAAA;YACE4H,OAAO,EAAEA,CAAA,KAAMxI,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD6G,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMxB,QAAQ,GAAGnF,SAAS,CAACE,eAAe,CAAC;EAC3C,MAAMsH,cAAc,GAAGtH,eAAe,KAAKF,SAAS,CAACsB,MAAM,GAAG,CAAC;;EAE/D;EACA,IAAI,CAAC6D,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,oBACE9F,OAAA;MAAKiH,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlH,OAAA;QAAKiH,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFlH,OAAA;UAAKiH,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlH,OAAA;YAAIiH,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EtH,OAAA;YAAGiH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFtH,OAAA;YACE4H,OAAO,EAAEA,CAAA,KAAMxI,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD6G,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAIA,oBACEtH,OAAA;IAAKiH,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExElH,OAAA;MAAKiH,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DlH,OAAA;QAAKiH,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClH,OAAA;UAAKiH,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDlH,OAAA;YAAKiH,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClH,OAAA;cACE4H,OAAO,EAAEA,CAAA,KAAMxI,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;cACxD6G,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eAE9DlH,OAAA,CAACN,WAAW;gBAACuH,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACTtH,OAAA;cAAAkH,QAAA,gBACElH,OAAA;gBAAIiH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAEzG,IAAI,CAACgD;cAAI;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEtH,OAAA;gBAAGiH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAC1B,EAACrG,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACsB,MAAM;cAAA;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtH,OAAA;YAAKiH,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtClH,OAAA;cAAKiH,SAAS,EAAG,gDACfhG,QAAQ,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAC/C,EAAE;cAAAiG,QAAA,gBACDlH,OAAA,CAACP,OAAO;gBAACwH,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BtH,OAAA;gBAAMiH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAE5B,UAAU,CAACrE,QAAQ;cAAC;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtH,OAAA;UAAKiH,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBlH,OAAA;YAAKiH,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDlH,OAAA;cACEiH,SAAS,EAAC,0DAA0D;cACpEmB,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAG,CAACxH,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACsB,MAAM,GAAI,GAAI;cAAG;YAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtH,OAAA;MAAKiH,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpClH,OAAA;QAAKiH,SAAS,EAAC,uFAAuF;QAAAC,QAAA,gBAEpGlH,OAAA;UAAKiH,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlH,OAAA;YAAIiH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAClD,OAAOpB,QAAQ,CAACrC,IAAI,KAAK,QAAQ,GAAGqC,QAAQ,CAACrC,IAAI,GAAG;UAAU;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,EAEJxB,QAAQ,CAACwC,KAAK,iBACbtI,OAAA;YAAKiH,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7ClH,OAAA;cACEuI,GAAG,EAAEzC,QAAQ,CAACwC,KAAM;cACpBE,GAAG,EAAC,kBAAkB;cACtBvB,SAAS,EAAC,sDAAsD;cAChEmB,KAAK,EAAE;gBAAEK,SAAS,EAAE;cAAQ,CAAE;cAC9BC,OAAO,EAAGX,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACI,KAAK,CAACO,OAAO,GAAG,MAAM;gBAC/B;gBACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBAC9CF,QAAQ,CAAC3B,SAAS,GAAG,gCAAgC;gBACrD2B,QAAQ,CAACG,SAAS,GAAG,+BAA+B;gBACpDhB,CAAC,CAACC,MAAM,CAACgB,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC;cAC3C;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNtH,OAAA;UAAKiH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BtB,mBAAmB,CAAC;QAAC;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGNtH,OAAA;UAAKiH,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDlH,OAAA;YACE4H,OAAO,EAAEvC,YAAa;YACtB6D,QAAQ,EAAErI,eAAe,KAAK,CAAE;YAChCoG,SAAS,EAAG,gFACVpG,eAAe,KAAK,CAAC,GACjB,8CAA8C,GAC9C,6CACL,EAAE;YAAAqG,QAAA,gBAEHlH,OAAA,CAACN,WAAW;cAACuH,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERa,cAAc,gBACbnI,OAAA;YACE4H,OAAO,EAAEtF,gBAAiB;YAC1B2E,SAAS,EAAC,yHAAyH;YAAAC,QAAA,gBAEnIlH,OAAA,CAACJ,OAAO;cAACqH,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETtH,OAAA;YACE4H,OAAO,EAAExC,QAAS;YAClB6B,SAAS,EAAC,uHAAuH;YAAAC,QAAA,GAClI,MAEC,eAAAlH,OAAA,CAACL,YAAY;cAACsH,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpH,EAAA,CAtkBID,QAAQ;EAAA,QACGZ,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAA4J,EAAA,GAHxBlJ,QAAQ;AAwkBd,eAAeA,QAAQ;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
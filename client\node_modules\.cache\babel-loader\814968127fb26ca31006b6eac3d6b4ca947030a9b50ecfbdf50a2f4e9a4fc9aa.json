{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlayClean.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n        const response = await getExamById(id);\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail';\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          points: correctAnswers * 10\n        }\n      };\n      console.log('Submitting quiz result:', reportData);\n      const response = await addReport(reportData);\n      console.log('Quiz submitted successfully:', response);\n      startTransition(() => {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails\n          }\n        });\n      });\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this);\n  }\n  if (!quiz || !questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => startTransition(() => navigate('/quiz')),\n              className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-6 h-6 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: quiz.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Question \", currentQuestion + 1, \" of \", questions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 px-4 py-2 rounded-lg ${timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${(currentQuestion + 1) / questions.length * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-4\",\n            children: currentQ.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), currentQ.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQ.image,\n              alt: \"Question\",\n              className: \"max-w-full h-auto rounded-lg shadow-md\",\n              onError: e => {\n                e.target.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 mb-8\",\n          children: currentQ.options.map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index);\n            const isSelected = answers[currentQuestion] === option;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAnswerSelect(option),\n              className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left transform hover:scale-105 active:scale-95 ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full flex items-center justify-center font-semibold ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestion === 0,\n            className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${currentQuestion === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSubmitQuiz,\n            className: \"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), \"Submit Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"fY1y2RfP35t8yXNHY5IyCXmOpUg=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlayClean;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "message", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "id", "navigate", "user", "state", "loading", "setLoading", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "console", "log", "_id", "token", "localStorage", "getItem", "error", "response", "success", "data", "length", "Array", "fill", "duration", "Date", "handleSubmitQuiz", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "percentage", "round", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "points", "totalQuestions", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQ", "isLastQuestion", "name", "style", "width", "image", "src", "alt", "onError", "e", "target", "display", "options", "option", "optionLetter", "String", "fromCharCode", "isSelected", "disabled", "_c", "QuizPlayClean", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlayClean.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { \n  Tb<PERSON><PERSON>, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        \n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById(id);\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    try {\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n\n        return {\n          question: question._id,\n          userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      const verdict = percentage >= 60 ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          points: correctAnswers * 10\n        }\n      };\n\n      console.log('Submitting quiz result:', reportData);\n      \n      const response = await addReport(reportData);\n      console.log('Quiz submitted successfully:', response);\n      \n      startTransition(() => {\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails\n          }\n        });\n      });\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      message.error('Failed to submit quiz');\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button \n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <button\n                onClick={() => startTransition(() => navigate('/quiz'))}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <TbArrowLeft className=\"w-6 h-6 text-gray-600\" />\n              </button>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{quiz.name}</h1>\n                <p className=\"text-sm text-gray-600\">\n                  Question {currentQuestion + 1} of {questions.length}\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${\n                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}>\n                <TbClock className=\"w-5 h-5\" />\n                <span className=\"font-semibold\">{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n          </div>\n          \n          {/* Progress bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto p-6\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\">\n          {/* Question */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {currentQ.name}\n            </h2>\n            \n            {currentQ.image && (\n              <div className=\"mb-6\">\n                <img \n                  src={currentQ.image} \n                  alt=\"Question\" \n                  className=\"max-w-full h-auto rounded-lg shadow-md\"\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Options */}\n          <div className=\"space-y-4 mb-8\">\n            {currentQ.options.map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion] === option;\n              \n              return (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left transform hover:scale-105 active:scale-95 ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                >\n                  <div className=\"flex items-center gap-4\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center font-semibold ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg\">{option}</span>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex justify-between items-center\">\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                className=\"flex items-center gap-2 px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors\"\n              >\n                <TbCheck className=\"w-5 h-5\" />\n                Submit Quiz\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbArrowRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlayClean;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAK,CAAC,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFb,UAAU,CAAC,IAAI,CAAC;QAChBc,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpB,EAAE,CAAC;QAExC,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACmB,GAAG,EAAE;UACtB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAI,CAACF,KAAK,EAAE;YACVH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnD/B,OAAO,CAACoC,KAAK,CAAC,gCAAgC,CAAC;YAC/CxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;QAEA,MAAMyB,QAAQ,GAAG,MAAMhC,WAAW,CAACM,EAAE,CAAC;QACtCmB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACD,QAAQ,CAACE,IAAI,EAAE;YAClBvC,OAAO,CAACoC,KAAK,CAAC,qBAAqB,CAAC;YACpCxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAACyB,QAAQ,CAACE,IAAI,CAACpB,SAAS,IAAIkB,QAAQ,CAACE,IAAI,CAACpB,SAAS,CAACqB,MAAM,KAAK,CAAC,EAAE;YACpExC,OAAO,CAACoC,KAAK,CAAC,sCAAsC,CAAC;YACrDxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAM,OAAO,CAACmB,QAAQ,CAACE,IAAI,CAAC;UACtBnB,YAAY,CAACiB,QAAQ,CAACE,IAAI,CAACpB,SAAS,CAAC;UACrCK,UAAU,CAAC,IAAIiB,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACpB,SAAS,CAACqB,MAAM,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;UAC9DhB,WAAW,CAACW,QAAQ,CAACE,IAAI,CAACI,QAAQ,GAAG,EAAE,CAAC;UACxCf,YAAY,CAAC,IAAIgB,IAAI,CAAC,CAAC,CAAC;UACxBd,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEM,QAAQ,CAACE,IAAI,CAAC;QACzD,CAAC,MAAM;UACLT,OAAO,CAACM,KAAK,CAAC,iBAAiB,EAAEC,QAAQ,CAACrC,OAAO,CAAC;UAClDA,OAAO,CAACoC,KAAK,CAACC,QAAQ,CAACrC,OAAO,IAAI,qBAAqB,CAAC;UACxDJ,eAAe,CAAC,MAAM;YACpBgB,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CpC,OAAO,CAACoC,KAAK,CAAC,wCAAwC,CAAC;QACvDxC,eAAe,CAAC,MAAM;UACpBgB,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIL,EAAE,IAAIE,IAAI,EAAE;MACdgB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAClB,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMgC,gBAAgB,GAAGlD,WAAW,CAAC,YAAY;IAC/C,IAAI;MACF,IAAImD,WAAW,GAAGjC,IAAI;MACtB,IAAI,CAACiC,WAAW,IAAI,CAACA,WAAW,CAACd,GAAG,EAAE;QACpC,MAAMe,UAAU,GAAGb,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIY,UAAU,EAAE;UACd,IAAI;YACFD,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOX,KAAK,EAAE;YACdN,OAAO,CAACM,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvDxC,eAAe,CAAC,MAAM;cACpBgB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACkC,WAAW,IAAI,CAACA,WAAW,CAACd,GAAG,EAAE;QACpChC,OAAO,CAACoC,KAAK,CAAC,2CAA2C,CAAC;QAC1DxC,eAAe,CAAC,MAAM;UACpBgB,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAMsC,OAAO,GAAG,IAAIN,IAAI,CAAC,CAAC;MAC1B,MAAMO,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGvB,SAAS,IAAI,IAAI,CAAC;MAE1D,IAAI2B,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGpC,SAAS,CAACqC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGpC,OAAO,CAACmC,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLG,QAAQ,EAAEA,QAAQ,CAACzB,GAAG;UACtB2B,UAAU;UACVE,aAAa,EAAEJ,QAAQ,CAACI,aAAa;UACrCD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAME,UAAU,GAAGV,IAAI,CAACW,KAAK,CAAET,cAAc,GAAGnC,SAAS,CAACqB,MAAM,GAAI,GAAG,CAAC;MACxE,MAAMwB,OAAO,GAAGF,UAAU,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM;MAElD,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAEvD,EAAE;QACRE,IAAI,EAAEiC,WAAW,CAACd,GAAG;QACrBmC,MAAM,EAAE;UACNb,cAAc;UACdc,YAAY,EAAEjD,SAAS,CAACqB,MAAM,GAAGc,cAAc;UAC/CQ,UAAU;UACVO,KAAK,EAAEP,UAAU;UACjBE,OAAO,EAAEA,OAAO;UAChBb,SAAS;UACTmB,MAAM,EAAEhB,cAAc,GAAG;QAC3B;MACF,CAAC;MAEDxB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkC,UAAU,CAAC;MAElD,MAAM5B,QAAQ,GAAG,MAAM/B,SAAS,CAAC2D,UAAU,CAAC;MAC5CnC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEM,QAAQ,CAAC;MAErDzC,eAAe,CAAC,MAAM;QACpBgB,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAC7BG,KAAK,EAAE;YACLgD,UAAU;YACVR,cAAc;YACdiB,cAAc,EAAEpD,SAAS,CAACqB,MAAM;YAChCW,SAAS;YACTI;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CpC,OAAO,CAACoC,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC,EAAE,CAACT,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEZ,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEvD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAI+B,QAAQ,IAAI,CAAC,EAAE;MACjBoB,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAM2B,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B/C,WAAW,CAACgD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAAC/C,QAAQ,EAAEoB,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAM+B,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC,GAAGvD,OAAO,CAAC;IAC/BuD,UAAU,CAACzD,eAAe,CAAC,GAAGwD,MAAM;IACpCrD,UAAU,CAACsD,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI1D,eAAe,GAAGF,SAAS,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC1ClB,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAM2D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI3D,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM4D,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAG/B,IAAI,CAACC,KAAK,CAAC6B,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,IAAIvE,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK+E,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGhF,OAAA;QAAK+E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhF,OAAA;UAAK+E,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGpF,OAAA;UAAG+E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC3E,IAAI,IAAI,CAACE,SAAS,CAACqB,MAAM,EAAE;IAC9B,oBACEhC,OAAA;MAAK+E,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGhF,OAAA;QAAK+E,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFhF,OAAA;UAAK+E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhF,OAAA;YAAI+E,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFpF,OAAA;YAAG+E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/EpF,OAAA;YACEqF,OAAO,EAAEA,CAAA,KAAMjG,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxD2E,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,QAAQ,GAAG3E,SAAS,CAACE,eAAe,CAAC;EAC3C,MAAM0E,cAAc,GAAG1E,eAAe,KAAKF,SAAS,CAACqB,MAAM,GAAG,CAAC;EAE/D,oBACEhC,OAAA;IAAK+E,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEhF,OAAA;MAAK+E,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DhF,OAAA;QAAK+E,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ChF,OAAA;UAAK+E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDhF,OAAA;YAAK+E,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtChF,OAAA;cACEqF,OAAO,EAAEA,CAAA,KAAMjG,eAAe,CAAC,MAAMgB,QAAQ,CAAC,OAAO,CAAC,CAAE;cACxD2E,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eAE9DhF,OAAA,CAACN,WAAW;gBAACqF,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACTpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAI+E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAEvE,IAAI,CAAC+E;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEpF,OAAA;gBAAG+E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAC1B,EAACnE,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACqB,MAAM;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtChF,OAAA;cAAK+E,SAAS,EAAG,gDACf9D,QAAQ,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAC/C,EAAE;cAAA+D,QAAA,gBACDhF,OAAA,CAACP,OAAO;gBAACsF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BpF,OAAA;gBAAM+E,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEP,UAAU,CAACxD,QAAQ;cAAC;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpF,OAAA;UAAK+E,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBhF,OAAA;YAAK+E,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDhF,OAAA;cACE+E,SAAS,EAAC,0DAA0D;cACpEU,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAG,CAAC7E,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACqB,MAAM,GAAI,GAAI;cAAG;YAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA;MAAK+E,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpChF,OAAA;QAAK+E,SAAS,EAAC,uFAAuF;QAAAC,QAAA,gBAEpGhF,OAAA;UAAK+E,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhF,OAAA;YAAI+E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAClDM,QAAQ,CAACE;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EAEJE,QAAQ,CAACK,KAAK,iBACb3F,OAAA;YAAK+E,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBhF,OAAA;cACE4F,GAAG,EAAEN,QAAQ,CAACK,KAAM;cACpBE,GAAG,EAAC,UAAU;cACdd,SAAS,EAAC,wCAAwC;cAClDe,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACP,KAAK,CAACQ,OAAO,GAAG,MAAM;cACjC;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNpF,OAAA;UAAK+E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BM,QAAQ,CAACY,OAAO,CAAClD,GAAG,CAAC,CAACmD,MAAM,EAAEjD,KAAK,KAAK;YACvC,MAAMkD,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGpD,KAAK,CAAC;YACpD,MAAMqD,UAAU,GAAGxF,OAAO,CAACF,eAAe,CAAC,KAAKsF,MAAM;YAEtD,oBACEnG,OAAA;cAEEqF,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAAC+B,MAAM,CAAE;cAC1CpB,SAAS,EAAG,kHACVwB,UAAU,GACN,0CAA0C,GAC1C,iEACL,EAAE;cAAAvB,QAAA,eAEHhF,OAAA;gBAAK+E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtChF,OAAA;kBAAK+E,SAAS,EAAG,uEACfwB,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;kBAAAvB,QAAA,EACAoB;gBAAY;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNpF,OAAA;kBAAM+E,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEmB;gBAAM;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC,GAjBDlC,KAAK;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBJ,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpF,OAAA;UAAK+E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDhF,OAAA;YACEqF,OAAO,EAAEb,YAAa;YACtBgC,QAAQ,EAAE3F,eAAe,KAAK,CAAE;YAChCkE,SAAS,EAAG,gFACVlE,eAAe,KAAK,CAAC,GACjB,8CAA8C,GAC9C,6CACL,EAAE;YAAAmE,QAAA,gBAEHhF,OAAA,CAACN,WAAW;cAACqF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERG,cAAc,gBACbvF,OAAA;YACEqF,OAAO,EAAEhD,gBAAiB;YAC1B0C,SAAS,EAAC,yHAAyH;YAAAC,QAAA,gBAEnIhF,OAAA,CAACJ,OAAO;cAACmF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETpF,OAAA;YACEqF,OAAO,EAAEd,QAAS;YAClBQ,SAAS,EAAC,uHAAuH;YAAAC,QAAA,GAClI,MAEC,eAAAhF,OAAA,CAACL,YAAY;cAACoF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CApXID,QAAQ;EAAA,QACGZ,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAAkH,EAAA,GAHxBxG,QAAQ;AAsXd,eAAeyG,aAAa;AAAC,IAAAD,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
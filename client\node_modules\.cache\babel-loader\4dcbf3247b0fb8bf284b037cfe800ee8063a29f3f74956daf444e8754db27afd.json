{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Suspense, lazy } from \"react\";\nimport \"./stylesheets/theme.css\";\nimport \"./stylesheets/alignments.css\";\nimport \"./stylesheets/textelements.css\";\nimport \"./stylesheets/form-elements.css\";\nimport \"./stylesheets/custom-components.css\";\nimport \"./stylesheets/layout.css\";\nimport \"./styles/modern.css\";\nimport \"./styles/animations.css\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Loader from \"./components/Loader\";\nimport { useSelector } from \"react-redux\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { ErrorBoundary } from \"./components/modern\";\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\n\n// Immediate load components (critical for initial render)\nimport Login from \"./pages/common/Login\";\nimport Register from \"./pages/common/Register\";\nimport Home from \"./pages/common/Home\";\n\n// Lazy load components for better performance\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = /*#__PURE__*/lazy(_c = () => import(\"./pages/user/Quiz\"));\n_c2 = Quiz;\nconst QuizPlay = /*#__PURE__*/lazy(_c3 = () => import(\"./pages/user/Quiz/QuizPlay\"));\n_c4 = QuizPlay;\nconst QuizResult = /*#__PURE__*/lazy(_c5 = () => import(\"./pages/user/Quiz/QuizResult\"));\n_c6 = QuizResult;\nconst Exams = /*#__PURE__*/lazy(_c7 = () => import(\"./pages/admin/Exams\"));\n_c8 = Exams;\nconst AddEditExam = /*#__PURE__*/lazy(_c9 = () => import(\"./pages/admin/Exams/AddEditExam\"));\n_c10 = AddEditExam;\nconst Users = /*#__PURE__*/lazy(_c11 = () => import(\"./pages/admin/Users\"));\n_c12 = Users;\nconst AdminDashboard = /*#__PURE__*/lazy(_c13 = () => import(\"./pages/admin/Dashboard\"));\n_c14 = AdminDashboard;\nconst TrialPage = /*#__PURE__*/lazy(_c15 = () => import(\"./pages/trial/TrialPage\"));\n_c16 = TrialPage;\nconst WriteExam = /*#__PURE__*/lazy(_c17 = () => import(\"./pages/user/WriteExam\"));\n_c18 = WriteExam;\nconst UserReports = /*#__PURE__*/lazy(_c19 = () => import(\"./pages/user/UserReports\"));\n_c20 = UserReports;\nconst AdminReports = /*#__PURE__*/lazy(_c21 = () => import(\"./pages/admin/AdminReports\"));\n_c22 = AdminReports;\nconst StudyMaterial = /*#__PURE__*/lazy(_c23 = () => import(\"./pages/user/StudyMaterial\"));\n_c24 = StudyMaterial;\nconst Ranking = /*#__PURE__*/lazy(_c25 = () => import(\"./pages/user/Ranking\"));\n_c26 = Ranking;\nconst RankingErrorBoundary = /*#__PURE__*/lazy(_c27 = () => import(\"./components/RankingErrorBoundary\"));\n_c28 = RankingErrorBoundary;\nconst Profile = /*#__PURE__*/lazy(_c29 = () => import(\"./pages/common/Profile\"));\n_c30 = Profile;\nconst AboutUs = /*#__PURE__*/lazy(_c31 = () => import(\"./pages/user/AboutUs\"));\n_c32 = AboutUs;\nconst Forum = /*#__PURE__*/lazy(_c33 = () => import(\"./pages/common/Forum\"));\n_c34 = Forum;\nconst Test = /*#__PURE__*/lazy(_c35 = () => import(\"./pages/user/Test\"));\n_c36 = Test;\nconst Chat = /*#__PURE__*/lazy(_c37 = () => import(\"./pages/user/Chat\"));\n_c38 = Chat;\nconst Plans = /*#__PURE__*/lazy(_c39 = () => import(\"./pages/user/Plans/Plans\"));\n_c40 = Plans;\nconst Hub = /*#__PURE__*/lazy(_c41 = () => import(\"./pages/user/Hub\"));\n_c42 = Hub;\nconst AdminStudyMaterials = /*#__PURE__*/lazy(_c43 = () => import(\"./pages/admin/StudyMaterials\"));\n_c44 = AdminStudyMaterials;\nconst AdminNotifications = /*#__PURE__*/lazy(_c45 = () => import(\"./pages/admin/Notifications/AdminNotifications\"));\n_c46 = AdminNotifications;\nconst DebugAuth = /*#__PURE__*/lazy(_c47 = () => import(\"./components/DebugAuth\"));\n_c48 = DebugAuth;\nconst RankingDemo = /*#__PURE__*/lazy(_c49 = () => import(\"./components/modern/RankingDemo\"));\n\n// Global error handler for CSS style errors\n_c50 = RankingDemo;\nwindow.addEventListener('error', event => {\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Error caught and handled:', event.message);\n    event.preventDefault();\n    return false;\n  }\n});\n\n// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection', event => {\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\n    event.preventDefault();\n  }\n});\n// Fast loading component for lazy routes\nconst FastLoader = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 font-medium\",\n      children: \"Loading page...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-400 text-sm mt-2\",\n      children: \"Please wait a moment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 68,\n  columnNumber: 3\n}, this);\n_c51 = FastLoader;\nfunction App() {\n  _s();\n  const {\n    loading\n  } = useSelector(state => state.loader);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ranking-demo\",\n            element: /*#__PURE__*/_jsxDEV(RankingDemo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/trial\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(TrialPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/test\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/forum\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Forum, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/chat\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/plans\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Plans, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/hub\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Hub, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/quiz\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Quiz, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/write-exam/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(WriteExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/result\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(QuizResult, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/play\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(UserReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/study-material\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(StudyMaterial, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/ranking\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(RankingErrorBoundary, {\n                  children: /*#__PURE__*/_jsxDEV(Ranking, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/about-us\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/users\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/add\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/edit/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/study-materials\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminStudyMaterials, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/notifications\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminNotifications, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/debug\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(DebugAuth, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c52 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52;\n$RefreshReg$(_c, \"Quiz$lazy\");\n$RefreshReg$(_c2, \"Quiz\");\n$RefreshReg$(_c3, \"QuizPlay$lazy\");\n$RefreshReg$(_c4, \"QuizPlay\");\n$RefreshReg$(_c5, \"QuizResult$lazy\");\n$RefreshReg$(_c6, \"QuizResult\");\n$RefreshReg$(_c7, \"Exams$lazy\");\n$RefreshReg$(_c8, \"Exams\");\n$RefreshReg$(_c9, \"AddEditExam$lazy\");\n$RefreshReg$(_c10, \"AddEditExam\");\n$RefreshReg$(_c11, \"Users$lazy\");\n$RefreshReg$(_c12, \"Users\");\n$RefreshReg$(_c13, \"AdminDashboard$lazy\");\n$RefreshReg$(_c14, \"AdminDashboard\");\n$RefreshReg$(_c15, \"TrialPage$lazy\");\n$RefreshReg$(_c16, \"TrialPage\");\n$RefreshReg$(_c17, \"WriteExam$lazy\");\n$RefreshReg$(_c18, \"WriteExam\");\n$RefreshReg$(_c19, \"UserReports$lazy\");\n$RefreshReg$(_c20, \"UserReports\");\n$RefreshReg$(_c21, \"AdminReports$lazy\");\n$RefreshReg$(_c22, \"AdminReports\");\n$RefreshReg$(_c23, \"StudyMaterial$lazy\");\n$RefreshReg$(_c24, \"StudyMaterial\");\n$RefreshReg$(_c25, \"Ranking$lazy\");\n$RefreshReg$(_c26, \"Ranking\");\n$RefreshReg$(_c27, \"RankingErrorBoundary$lazy\");\n$RefreshReg$(_c28, \"RankingErrorBoundary\");\n$RefreshReg$(_c29, \"Profile$lazy\");\n$RefreshReg$(_c30, \"Profile\");\n$RefreshReg$(_c31, \"AboutUs$lazy\");\n$RefreshReg$(_c32, \"AboutUs\");\n$RefreshReg$(_c33, \"Forum$lazy\");\n$RefreshReg$(_c34, \"Forum\");\n$RefreshReg$(_c35, \"Test$lazy\");\n$RefreshReg$(_c36, \"Test\");\n$RefreshReg$(_c37, \"Chat$lazy\");\n$RefreshReg$(_c38, \"Chat\");\n$RefreshReg$(_c39, \"Plans$lazy\");\n$RefreshReg$(_c40, \"Plans\");\n$RefreshReg$(_c41, \"Hub$lazy\");\n$RefreshReg$(_c42, \"Hub\");\n$RefreshReg$(_c43, \"AdminStudyMaterials$lazy\");\n$RefreshReg$(_c44, \"AdminStudyMaterials\");\n$RefreshReg$(_c45, \"AdminNotifications$lazy\");\n$RefreshReg$(_c46, \"AdminNotifications\");\n$RefreshReg$(_c47, \"DebugAuth$lazy\");\n$RefreshReg$(_c48, \"DebugAuth\");\n$RefreshReg$(_c49, \"RankingDemo$lazy\");\n$RefreshReg$(_c50, \"RankingDemo\");\n$RefreshReg$(_c51, \"FastLoader\");\n$RefreshReg$(_c52, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "ProtectedRoute", "Loader", "useSelector", "ThemeProvider", "Error<PERSON>ou<PERSON><PERSON>", "AdminProtectedRoute", "<PERSON><PERSON>", "Register", "Home", "jsxDEV", "_jsxDEV", "Quiz", "_c", "_c2", "QuizPlay", "_c3", "_c4", "QuizResult", "_c5", "_c6", "<PERSON><PERSON>", "_c7", "_c8", "AddEditExam", "_c9", "_c10", "Users", "_c11", "_c12", "AdminDashboard", "_c13", "_c14", "TrialPage", "_c15", "_c16", "WriteExam", "_c17", "_c18", "UserReports", "_c19", "_c20", "AdminReports", "_c21", "_c22", "StudyMaterial", "_c23", "_c24", "Ranking", "_c25", "_c26", "RankingError<PERSON><PERSON><PERSON>ry", "_c27", "_c28", "Profile", "_c29", "_c30", "AboutUs", "_c31", "_c32", "Forum", "_c33", "_c34", "Test", "_c35", "_c36", "Cha<PERSON>", "_c37", "_c38", "Plans", "_c39", "_c40", "<PERSON><PERSON>", "_c41", "_c42", "AdminStudyMaterials", "_c43", "_c44", "AdminNotifications", "_c45", "_c46", "DebugAuth", "_c47", "_c48", "RankingDemo", "_c49", "_c50", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "FastLoader", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c51", "App", "_s", "loading", "state", "loader", "path", "element", "fallback", "_c52", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense, lazy } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\r\n\r\n// Immediate load components (critical for initial render)\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport Home from \"./pages/common/Home\";\r\n\r\n// Lazy load components for better performance\r\nconst Quiz = lazy(() => import(\"./pages/user/Quiz\"));\r\nconst QuizPlay = lazy(() => import(\"./pages/user/Quiz/QuizPlay\"));\r\nconst QuizResult = lazy(() => import(\"./pages/user/Quiz/QuizResult\"));\r\nconst Exams = lazy(() => import(\"./pages/admin/Exams\"));\r\nconst AddEditExam = lazy(() => import(\"./pages/admin/Exams/AddEditExam\"));\r\nconst Users = lazy(() => import(\"./pages/admin/Users\"));\r\nconst AdminDashboard = lazy(() => import(\"./pages/admin/Dashboard\"));\r\nconst TrialPage = lazy(() => import(\"./pages/trial/TrialPage\"));\r\nconst WriteExam = lazy(() => import(\"./pages/user/WriteExam\"));\r\nconst UserReports = lazy(() => import(\"./pages/user/UserReports\"));\r\nconst AdminReports = lazy(() => import(\"./pages/admin/AdminReports\"));\r\nconst StudyMaterial = lazy(() => import(\"./pages/user/StudyMaterial\"));\r\nconst Ranking = lazy(() => import(\"./pages/user/Ranking\"));\r\nconst RankingErrorBoundary = lazy(() => import(\"./components/RankingErrorBoundary\"));\r\nconst Profile = lazy(() => import(\"./pages/common/Profile\"));\r\nconst AboutUs = lazy(() => import(\"./pages/user/AboutUs\"));\r\nconst Forum = lazy(() => import(\"./pages/common/Forum\"));\r\nconst Test = lazy(() => import(\"./pages/user/Test\"));\r\nconst Chat = lazy(() => import(\"./pages/user/Chat\"));\r\nconst Plans = lazy(() => import(\"./pages/user/Plans/Plans\"));\r\nconst Hub = lazy(() => import(\"./pages/user/Hub\"));\r\nconst AdminStudyMaterials = lazy(() => import(\"./pages/admin/StudyMaterials\"));\r\nconst AdminNotifications = lazy(() => import(\"./pages/admin/Notifications/AdminNotifications\"));\r\nconst DebugAuth = lazy(() => import(\"./components/DebugAuth\"));\r\nconst RankingDemo = lazy(() => import(\"./components/modern/RankingDemo\"));\r\n\r\n// Global error handler for CSS style errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// Fast loading component for lazy routes\r\nconst FastLoader = () => (\r\n  <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n    <div className=\"text-center\">\r\n      <div className=\"relative\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n        <div className=\"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"></div>\r\n      </div>\r\n      <p className=\"text-gray-600 font-medium\">Loading page...</p>\r\n      <p className=\"text-gray-400 text-sm mt-2\">Please wait a moment</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        {loading && <Loader />}\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n\r\n          {/* Trial Route (No authentication required) */}\r\n          <Route path=\"/trial\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <TrialPage />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Forum />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/chat\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Chat />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/plans\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Plans />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Hub />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Quiz />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <WriteExam />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <QuizResult />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <UserReports />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <StudyMaterial />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <RankingErrorBoundary>\r\n                    <Ranking />\r\n                  </RankingErrorBoundary>\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/about-us\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AboutUs />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminDashboard />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Users />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Exams />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminStudyMaterials />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminReports />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/notifications\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminNotifications />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/debug\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <DebugAuth />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AAC7C,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAO,gCAAgC;AACvC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAC5C,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,IAAI,MAAM,qBAAqB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,gBAAGf,IAAI,CAAAgB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA/CF,IAAI;AACV,MAAMG,QAAQ,gBAAGlB,IAAI,CAAAmB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAA5DF,QAAQ;AACd,MAAMG,UAAU,gBAAGrB,IAAI,CAAAsB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,GAAA,GAAhEF,UAAU;AAChB,MAAMG,KAAK,gBAAGxB,IAAI,CAAAyB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,GAAA,GAAlDF,KAAK;AACX,MAAMG,WAAW,gBAAG3B,IAAI,CAAA4B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,KAAK,gBAAG9B,IAAI,CAAA+B,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AACX,MAAMG,cAAc,gBAAGjC,IAAI,CAAAkC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA/DF,cAAc;AACpB,MAAMG,SAAS,gBAAGpC,IAAI,CAAAqC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA1DF,SAAS;AACf,MAAMG,SAAS,gBAAGvC,IAAI,CAAAwC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAG1C,IAAI,CAAA2C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAA7DF,WAAW;AACjB,MAAMG,YAAY,gBAAG7C,IAAI,CAAA8C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAhEF,YAAY;AAClB,MAAMG,aAAa,gBAAGhD,IAAI,CAAAiD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAjEF,aAAa;AACnB,MAAMG,OAAO,gBAAGnD,IAAI,CAAAoD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,oBAAoB,gBAAGtD,IAAI,CAAAuD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAA/EF,oBAAoB;AAC1B,MAAMG,OAAO,gBAAGzD,IAAI,CAAA0D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAvDF,OAAO;AACb,MAAMG,OAAO,gBAAG5D,IAAI,CAAA6D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,KAAK,gBAAG/D,IAAI,CAAAgE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAnDF,KAAK;AACX,MAAMG,IAAI,gBAAGlE,IAAI,CAAAmE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AACV,MAAMG,IAAI,gBAAGrE,IAAI,CAAAsE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AACV,MAAMG,KAAK,gBAAGxE,IAAI,CAAAyE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAAvDF,KAAK;AACX,MAAMG,GAAG,gBAAG3E,IAAI,CAAA4E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAA7CF,GAAG;AACT,MAAMG,mBAAmB,gBAAG9E,IAAI,CAAA+E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,IAAA,GAAzEF,mBAAmB;AACzB,MAAMG,kBAAkB,gBAAGjF,IAAI,CAAAkF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAAC;AAACC,IAAA,GAA1FF,kBAAkB;AACxB,MAAMG,SAAS,gBAAGpF,IAAI,CAAAqF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAGvF,IAAI,CAAAwF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;;AAEzE;AAAAC,IAAA,GAFMF,WAAW;AAGjBG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACvFC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEJ,KAAK,CAACC,OAAO,CAAC;IAClED,KAAK,CAACK,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvD,IAAIA,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,IAAID,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACrHC,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC;IACrFD,KAAK,CAACK,cAAc,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF;AACA,MAAME,UAAU,GAAGA,CAAA,kBACjBrF,OAAA;EAAKsF,SAAS,EAAC,4FAA4F;EAAAC,QAAA,eACzGvF,OAAA;IAAKsF,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BvF,OAAA;MAAKsF,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBvF,OAAA;QAAKsF,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnG3F,OAAA;QAAKsF,SAAS,EAAC;MAA0F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7G,CAAC,eACN3F,OAAA;MAAGsF,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC5D3F,OAAA;MAAGsF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,IAAA,GAXIP,UAAU;AAahB,SAASQ,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAQ,CAAC,GAAGvG,WAAW,CAAEwG,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACxD,oBACEjG,OAAA,CAACN,aAAa;IAAA6F,QAAA,eACZvF,OAAA,CAACP,aAAa;MAAA8F,QAAA,GACXQ,OAAO,iBAAI/F,OAAA,CAACT,MAAM;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtB3F,OAAA,CAACb,aAAa;QAAAoG,QAAA,eACdvF,OAAA,CAACZ,MAAM;UAAAmG,QAAA,gBAELvF,OAAA,CAACX,KAAK;YAAC6G,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEnG,OAAA,CAACJ,KAAK;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3C3F,OAAA,CAACX,KAAK;YAAC6G,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEnG,OAAA,CAACH,QAAQ;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD3F,OAAA,CAACX,KAAK;YAAC6G,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEnG,OAAA,CAACF,IAAI;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrC3F,OAAA,CAACX,KAAK;YAAC6G,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEnG,OAAA,CAACyE,WAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGxD3F,OAAA,CAACX,KAAK;YAAC6G,IAAI,EAAC,QAAQ;YAACC,OAAO,eAC1BnG,OAAA,CAACf,QAAQ;cAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjCvF,OAAA,CAACsB,SAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJ3F,OAAA,CAACX,KAAK;YAAC6G,IAAI,EAAC,OAAO;YAACC,OAAO,eACzBnG,OAAA,CAACf,QAAQ;cAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjCvF,OAAA,CAACoD,IAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJ3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAACiD,KAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,UAAU;YACfC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAAC2C,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAAC2C,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACuD,IAAI;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAAC0D,KAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAAC6D,GAAG;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAACC,IAAI;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAACyB,SAAS;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAACO,UAAU;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAIF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACI,QAAQ;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAAC4B,WAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAACkC,aAAa;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACf,QAAQ;gBAACmH,QAAQ,eAAEpG,OAAA,CAACqF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCvF,OAAA,CAACwC,oBAAoB;kBAAA+C,QAAA,eACnBvF,OAAA,CAACqC,OAAO;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAAC8C,OAAO;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACmB,cAAc;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACgB,KAAK;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACU,KAAK;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACa,WAAW;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,uBAAuB;YAC5BC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACa,WAAW;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACgE,mBAAmB;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAAC+B,YAAY;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACmE,kBAAkB;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF3F,OAAA,CAACX,KAAK;YACJ6G,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLnG,OAAA,CAACV,cAAc;cAAAiG,QAAA,eACbvF,OAAA,CAACL,mBAAmB;gBAAA4F,QAAA,eAClBvF,OAAA,CAACsE,SAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACG,EAAA,CA9QQD,GAAG;EAAA,QACUrG,WAAW;AAAA;AAAA6G,IAAA,GADxBR,GAAG;AAgRZ,eAAeA,GAAG;AAAC,IAAA3F,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAiB,IAAA,EAAAS,IAAA;AAAAC,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA3C,IAAA;AAAA2C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAxC,IAAA;AAAAwC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAArC,IAAA;AAAAqC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAlC,IAAA;AAAAkC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAA/B,IAAA;AAAA+B,YAAA,CAAA9B,IAAA;AAAA8B,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAA3B,IAAA;AAAA2B,YAAA,CAAAV,IAAA;AAAAU,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}